<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.subfg.repository.mapper.FgFamilyGroupMapper">

    <!-- 家庭组基本信息结果映射 -->
    <resultMap id="BaseResultMap" type="com.subfg.domain.entity.fg.FgFamilyGroupPo">
        <id column="family_group_id" property="familyGroupId" />
        <result column="family_group_name" property="familyGroupName" />
        <result column="description" property="description" />
        <result column="group_type" property="groupType" />
        <result column="family_group_status" property="familyGroupStatus" />
        <result column="invite_url" property="inviteUrl" />
        <result column="tags" property="tags" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="product_id" property="productId" />
        <result column="category_id" property="categoryId" />
        <result column="region_id" property="regionId" />
        <result column="plan_id" property="planId" />
        <result column="amount" property="amount" />
        <result column="billing_cycle" property="billingCycle" />
        <result column="current_member_count" property="currentMemberCount" />
        <result column="sum_vacancy" property="sumVacancy" />
        <result column="deadline" property="deadline" />
        <result column="group_leader_id" property="groupLeaderId" />
        <result column="create_user_id" property="createUserId" />
        <result column="fg_create_time" property="createTime" />
        <result column="fg_update_time" property="updateTime" />
        <result column="latest_join_time" property="latestJoinTime" />
        <result column="launch_time" property="launchTime" />
        <result column="is_converted" property="isConverted" />
        <result column="review_time" property="reviewTime" />
        <result column="review_user" property="reviewUser" />
        <result column="review_remark" property="reviewRemark" />
        <result column="review_picture" property="reviewPicture" />
        <result column="subscribe_start_time" property="subscribeStartTime" />
        <result column="subscribe_end_time" property="subscribeEndTime" />
    </resultMap>

    <!-- 家庭组成员结果映射 -->
    <resultMap id="MemberResultMap" type="com.subfg.domain.entity.fg.FgMemberPo">
        <id column="member_id" property="memberId" />
        <result column="family_group_id" property="familyGroupId" />
        <result column="user_id" property="userId" />
        <result column="status" property="status" />
        <result column="invitation_time" property="invitationTime" />
        <result column="active_time" property="activeTime" />
        <result column="service_start_time" property="serviceStartTime" />
        <result column="service_end_time" property="serviceEndTime" />
        <result column="confirm_time" property="confirmTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="family_group_owner" property="familyGroupOwner" />
        <result column="is_leader" property="isLeader" />
    </resultMap>

    <!-- 家庭组详情结果映射（包含成员列表） -->
    <resultMap id="FamilyGroupDetailResultMap" type="com.subfg.domain.vo.FamilyGroupDetailVo">
        <association property="familyGroup" javaType="com.subfg.domain.entity.fg.FgFamilyGroupPo">
            <id column="family_group_id" property="familyGroupId" />
            <result column="family_group_name" property="familyGroupName" />
            <result column="description" property="description" />
            <result column="group_type" property="groupType" />
            <result column="family_group_status" property="familyGroupStatus" />
            <result column="invite_url" property="inviteUrl" />
            <result column="tags" property="tags" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
            <result column="product_id" property="productId" />
            <result column="category_id" property="categoryId" />
            <result column="region_id" property="regionId" />
            <result column="plan_id" property="planId" />
            <result column="amount" property="amount" />
            <result column="billing_cycle" property="billingCycle" />
            <result column="current_member_count" property="currentMemberCount" />
            <result column="sum_vacancy" property="sumVacancy" />
            <result column="deadline" property="deadline" />
            <result column="group_leader_id" property="groupLeaderId" />
            <result column="create_user_id" property="createUserId" />
            <result column="fg_create_time" property="createTime" />
            <result column="fg_update_time" property="updateTime" />
            <result column="latest_join_time" property="latestJoinTime" />
            <result column="launch_time" property="launchTime" />
            <result column="is_converted" property="isConverted" />
        </association>
        <collection property="memberList" ofType="com.subfg.domain.entity.fg.FgMemberPo">
            <id column="member_id" property="memberId" />
            <result column="family_group_id" property="familyGroupId" />
            <result column="user_id" property="userId" />
            <result column="status" property="status" />
            <result column="invitation_time" property="invitationTime" />
            <result column="active_time" property="activeTime" />
            <result column="service_start_time" property="serviceStartTime" />
            <result column="service_end_time" property="serviceEndTime" />
            <result column="confirm_time" property="confirmTime" />
            <result column="member_create_time" property="createTime" />
            <result column="member_update_time" property="updateTime" />
            <result column="family_group_owner" property="familyGroupOwner" />
            <result column="is_leader" property="isLeader" />
        </collection>
    </resultMap>

    <!-- 家庭组基本字段 -->
    <sql id="FamilyGroup_Column_List">
        fg.family_group_id, fg.family_group_name, fg.description, fg.group_type,
        fg.family_group_status, fg.invite_url, fg.tags, fg.product_id, fg.category_id,
        fg.region_id, fg.plan_id, fg.amount, fg.billing_cycle, fg.current_member_count,
        fg.sum_vacancy, fg.deadline, fg.group_leader_id, fg.create_user_id,
        fg.create_time as fg_create_time, fg.update_time as fg_update_time,
        fg.latest_join_time, fg.launch_time, fg.is_converted, fg.review_time,
        fg.review_user, fg.review_remark, fg.review_picture, fg.subscribe_start_time, fg.subscribe_end_time
    </sql>

    <!-- 家庭组成员字段 -->
    <sql id="Member_Column_List">
        m.member_id, m.family_group_id, m.user_id, m.status, m.invitation_time,
        m.active_time, m.service_start_time, m.service_end_time, m.confirm_time,
        m.create_time as member_create_time, m.update_time as member_update_time,
        m.family_group_owner, m.is_leader
    </sql>

    <!-- 联表查询家庭组详情（包含成员信息） -->
    <select id="selectFamilyGroupDetailById" resultMap="FamilyGroupDetailResultMap">
        SELECT
            <include refid="FamilyGroup_Column_List" />,
            <include refid="Member_Column_List" />
        FROM fg_family_group fg
        LEFT JOIN fg_member m ON fg.family_group_id = m.family_group_id
        WHERE fg.family_group_id = #{familyGroupId}
        ORDER BY m.create_time ASC
    </select>

    <!-- 根据家庭组类型和状态查询家庭组列表 -->
    <select id="selectByTypeAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="FamilyGroup_Column_List" />
        FROM fg_family_group fg
        WHERE fg.group_type = #{groupType}
        AND fg.family_group_status = #{status}
        ORDER BY fg.create_time DESC
    </select>

    <!-- 根据家庭组状态查询家庭组列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="FamilyGroup_Column_List" />
        FROM fg_family_group fg
        WHERE fg.family_group_status = #{status}
        ORDER BY fg.create_time DESC
    </select>

    <!-- 根据团长ID查询家庭组列表 -->
    <select id="selectByGroupLeaderId" resultMap="BaseResultMap">
        SELECT <include refid="FamilyGroup_Column_List" />
        FROM fg_family_group fg
        WHERE fg.group_leader_id = #{groupLeaderId}
        <if test="groupType != null">
            AND fg.group_type = #{groupType}
        </if>
        ORDER BY fg.create_time DESC
    </select>

    <!-- 查询最新通过审核的家庭组列表 -->
    <select id="selectLatestReviewedGroups" resultMap="BaseResultMap">
        SELECT <include refid="FamilyGroup_Column_List" />
        FROM fg_family_group fg
        WHERE fg.family_group_status = 1
        AND fg.review_time IS NOT NULL
        ORDER BY fg.review_time DESC
        LIMIT #{limit}
    </select>

