<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.subfg.repository.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.subfg.domain.entity.user.UserPo">
        <id column="user_id" property="userId" />
        <result column="email" property="email" />
        <result column="phone" property="phone" />
        <result column="wechat" property="wechat" />
        <result column="password" property="password" />
        <result column="salt" property="salt" />
        <result column="role" property="role" />
        <result column="user_name" property="userName" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="motto" property="motto" />
        <result column="credit_score" property="creditScore" />
        <result column="ip_address" property="ipAddress" />
        <result column="create_ip" property="createIp" />
        <result column="last_online_time" property="lastOnlineTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="enable" property="enable" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, email, phone, wechat, password, salt, role, user_name, 
        avatar_url, motto, credit_score, ip_address, create_ip, 
        last_online_time, delete_time, enable, create_time
    </sql>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE email = #{email}
        AND delete_time IS NULL
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="selectByPhone" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE phone = #{phone}
        AND delete_time IS NULL
    </select>

    <!-- 根据微信号查询用户 -->
    <select id="selectByWechat" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE wechat = #{wechat}
        AND delete_time IS NULL
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUserName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE user_name = #{userName}
        AND delete_time IS NULL
    </select>

    <!-- 根据角色查询用户列表 -->
    <select id="selectByRole" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE role = #{role}
        AND delete_time IS NULL
        ORDER BY create_time DESC
    </select>

    <!-- 查询启用状态的用户列表 -->
    <select id="selectByEnable" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE enable = #{enable}
        AND delete_time IS NULL
        ORDER BY create_time DESC
    </select>

    <!-- 根据创建时间范围查询用户 -->
    <select id="selectByCreateTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        AND delete_time IS NULL
        ORDER BY create_time DESC
    </select>

    <!-- 根据最后在线时间查询活跃用户 -->
    <select id="selectActiveUsers" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE last_online_time >= #{lastOnlineTime}
        AND delete_time IS NULL
        ORDER BY last_online_time DESC
    </select>

    <!-- 查询信用分数范围内的用户 -->
    <select id="selectByCreditScoreRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE credit_score BETWEEN #{minScore} AND #{maxScore}
        AND delete_time IS NULL
        ORDER BY credit_score DESC
    </select>

    <!-- 统计用户数量 -->
    <select id="countUsers" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM user
        WHERE delete_time IS NULL
        <if test="role != null and role != ''">
            AND role = #{role}
        </if>
        <if test="enable != null">
            AND enable = #{enable}
        </if>
    </select>

    <!-- 更新用户最后在线时间 -->
    <update id="updateLastOnlineTime">
        UPDATE user
        SET last_online_time = #{lastOnlineTime}
        WHERE user_id = #{userId}
    </update>

    <!-- 更新用户密码 -->
    <update id="updatePassword">
        UPDATE user
        SET password = #{password}, salt = #{salt}
        WHERE user_id = #{userId}
    </update>

    <!-- 更新用户状态 -->
    <update id="updateEnable">
        UPDATE user
        SET enable = #{enable}
        WHERE user_id = #{userId}
    </update>

    <!-- 更新用户信用分数 -->
    <update id="updateCreditScore">
        UPDATE user
        SET credit_score = #{creditScore}
        WHERE user_id = #{userId}
    </update>

    <!-- 软删除用户 -->
    <update id="softDelete">
        UPDATE user
        SET delete_time = #{deleteTime}
        WHERE user_id = #{userId}
    </update>

    <!-- 批量更新用户状态 -->
    <update id="batchUpdateEnable">
        UPDATE user
        SET enable = #{enable}
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <!-- 查询已删除的用户 -->
    <select id="selectDeletedUsers" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE delete_time IS NOT NULL
        ORDER BY delete_time DESC
    </select>

    <!-- 根据IP地址查询用户 -->
    <select id="selectByIpAddress" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE (ip_address = #{ipAddress} OR create_ip = #{ipAddress})
        AND delete_time IS NULL
        ORDER BY create_time DESC
    </select>

</mapper>
