<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.subfg.repository.mapper.ConfigEmailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.subfg.domain.entity.config.ConfigEmailPo">
        <result column="id" property="id" />
        <result column="host" property="host" />
        <result column="account" property="account" />
        <result column="password" property="password" />
        <result column="port" property="port" />
        <result column="protocol" property="protocol" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, host, account, password, port, protocol
    </sql>

    <!-- 查询邮箱配置（返回加密数据，在Java代码中解密） -->
    <select id="selectEmailConfigDecrypted" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM config_email
        WHERE enable = 1
    </select>

    <!-- 根据ID查询邮箱配置（返回加密数据，在Java代码中解密） -->
    <select id="selectByIdDecrypted" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM config_email
        WHERE id = #{id}
    </select>

    <!-- 插入邮箱配置（加密敏感字段） -->
    <insert id="insertEncrypted" parameterType="com.subfg.domain.entity.config.ConfigEmailPo">
        INSERT INTO config_email (
            host,
            account,
            password,
            port,
            protocol
        ) VALUES (
            #{host},
            #{account},
            #{password},
            #{port},
            #{protocol}
        )
    </insert>

    <!-- 更新邮箱配置（加密敏感字段） -->
    <update id="updateEncrypted" parameterType="com.subfg.domain.entity.config.ConfigEmailPo">
        UPDATE config_email
        SET 
            host = #{host},
            account = #{account},
            password = #{password},
            port = #{port},
            protocol = #{protocol}
        WHERE id = #{id}
    </update>

</mapper>
