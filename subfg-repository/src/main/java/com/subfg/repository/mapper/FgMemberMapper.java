package com.subfg.repository.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.subfg.domain.entity.fg.FgMemberPo;

/**
 * 家庭组成员 Mapper 接口
 */
@Mapper
public interface FgMemberMapper extends BaseMapper<FgMemberPo> {

    /**
     * 根据家庭组ID查询成员列表
     *
     * @param familyGroupId 家庭组ID
     * @return 成员列表
     */
    List<FgMemberPo> selectByFamilyGroupId(@Param("familyGroupId") String familyGroupId);

    /**
     * 根据用户ID查询加入的家庭组成员记录
     *
     * @param userId 用户ID
     * @return 成员记录列表
     */
    List<FgMemberPo> selectByUserId(@Param("userId") String userId);

    /**
     * 根据家庭组ID和用户ID查询成员记录
     *
     * @param familyGroupId 家庭组ID
     * @param userId        用户ID
     * @return 成员记录
     */
    FgMemberPo selectByFamilyGroupIdAndUserId(@Param("familyGroupId") String familyGroupId,
                                             @Param("userId") String userId);

    /**
     * 根据成员状态查询成员列表
     *
     * @param status 成员状态
     * @return 成员列表
     */
    List<FgMemberPo> selectByStatus(@Param("status") Integer status);

    /**
     * 统计家庭组的成员数量
     *
     * @param familyGroupId 家庭组ID
     * @param status        成员状态（可为空，为空时统计所有状态）
     * @return 成员数量
     */
    Integer countByFamilyGroupId(@Param("familyGroupId") String familyGroupId,
                                @Param("status") Integer status);

    /**
     * 统计用户加入的家庭组数量
     *
     * @param userId 用户ID
     * @param status 成员状态（可为空，为空时统计所有状态）
     * @return 加入的家庭组数量
     */
    Integer countByUserId(@Param("userId") String userId,
                         @Param("status") Integer status);

    /**
     * 更新成员状态
     *
     * @param memberId   成员ID
     * @param status     新状态
     * @param updateTime 更新时间
     * @return 影响行数
     */
    int updateStatus(@Param("memberId") String memberId,
                    @Param("status") Integer status,
                    @Param("updateTime") Long updateTime);

    /**
     * 更新成员确认时间
     *
     * @param memberId    成员ID
     * @param confirmTime 确认时间
     * @return 影响行数
     */
    int updateConfirmTime(@Param("memberId") String memberId,
                         @Param("confirmTime") Long confirmTime);

    /**
     * 批量更新成员状态
     *
     * @param memberIds  成员ID列表
     * @param status     新状态
     * @param updateTime 更新时间
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("memberIds") List<String> memberIds,
                         @Param("status") Integer status,
                         @Param("updateTime") Long updateTime);

    /**
     * 查询即将过期的成员
     *
     * @param expireTime 过期时间阈值
     * @return 即将过期的成员列表
     */
    List<FgMemberPo> selectExpiringSoon(@Param("expireTime") Long expireTime);

    /**
     * 根据家庭组所有者查询成员列表
     *
     * @param familyGroupOwner 家庭组所有者
     * @return 成员列表
     */
    List<FgMemberPo> selectByFamilyGroupOwner(@Param("familyGroupOwner") String familyGroupOwner);

    /**
     * 删除家庭组的所有成员
     *
     * @param familyGroupId 家庭组ID
     * @return 影响行数
     */
    int deleteByFamilyGroupId(@Param("familyGroupId") String familyGroupId);
}
