<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.subfg</groupId>
        <artifactId>subfg-v3</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>subfg-repository</artifactId>
    <name>subfg-repository</name>
    <description>数据访问模块，包含 Repository 接口和实现、数据库配置</description>

    <properties>
        <java.version>21</java.version>
    </properties>

    <dependencies>
        <!-- 引用公共模块 -->
        <dependency>
            <groupId>com.subfg</groupId>
            <artifactId>subfg-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- 引用领域模块 -->
        <dependency>
            <groupId>com.subfg</groupId>
            <artifactId>subfg-domain</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- MyBatis Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>3.5.12</version>
        </dependency>
        
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-jsqlparser</artifactId>
            <version>3.5.12</version>
        </dependency>

        <!-- MySQL 驱动 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

</project>
