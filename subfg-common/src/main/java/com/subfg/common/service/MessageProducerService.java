package com.subfg.common.service;

import java.util.UUID;

import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import com.subfg.common.config.RabbitMQConfig;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息生产者服务
 * 提供各种类型的消息发送功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageProducerService {

    private final RabbitTemplate rabbitTemplate;

    /**
     * 发送用户通知消息
     *
     * @param userId  用户ID
     * @param title   通知标题
     * @param content 通知内容
     * @param type    通知类型
     */
    public void sendUserNotification(String userId, String title, String content, Integer type) {
        try {
            UserNotificationMessage message = UserNotificationMessage.builder()
                    .userId(userId)
                    .title(title)
                    .content(content)
                    .type(type)
                    .timestamp(System.currentTimeMillis())
                    .build();

            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.DIRECT_EXCHANGE,
                    RabbitMQConfig.USER_NOTIFICATION_ROUTING_KEY,
                    message,
                    correlationData
            );
            
            log.info("用户通知消息发送成功 - userId: {}, title: {}", userId, title);
        } catch (Exception e) {
            log.error("用户通知消息发送失败 - userId: {}, title: {}", userId, title, e);
            throw new RuntimeException("用户通知消息发送失败", e);
        }
    }

    /**
     * 发送邮件消息
     *
     * @param to      收件人
     * @param subject 邮件主题
     * @param content 邮件内容
     * @param type    邮件类型（1-文本，2-HTML）
     */
    public void sendEmailMessage(String to, String subject, String content, Integer type) {
        try {
            EmailMessage message = EmailMessage.builder()
                    .to(to)
                    .subject(subject)
                    .content(content)
                    .type(type)
                    .timestamp(System.currentTimeMillis())
                    .build();

            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.DIRECT_EXCHANGE,
                    RabbitMQConfig.EMAIL_ROUTING_KEY,
                    message,
                    correlationData
            );
            
            log.info("邮件消息发送成功 - to: {}, subject: {}", to, subject);
        } catch (Exception e) {
            log.error("邮件消息发送失败 - to: {}, subject: {}", to, subject, e);
            throw new RuntimeException("邮件消息发送失败", e);
        }
    }



    /**
     * 发送延时消息（使用TTL + 死信队列实现）
     *
     * @param exchange    交换机
     * @param routingKey  路由键
     * @param message     消息内容
     * @param delayMillis 延时毫秒数
     */
    public void sendDelayMessage(String exchange, String routingKey, Object message, long delayMillis) {
        try {
            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());

            rabbitTemplate.convertAndSend(exchange, routingKey, message, msg -> {
                // 设置消息TTL（生存时间）
                msg.getMessageProperties().setExpiration(String.valueOf(delayMillis));
                // 设置消息头，标识这是延时消息
                msg.getMessageProperties().setHeader("x-delay", delayMillis);
                return msg;
            }, correlationData);

            log.info("延时消息发送成功 - exchange: {}, routingKey: {}, delay: {}ms",
                    exchange, routingKey, delayMillis);
        } catch (Exception e) {
            log.error("延时消息发送失败 - exchange: {}, routingKey: {}", exchange, routingKey, e);
            throw new RuntimeException("延时消息发送失败", e);
        }
    }

    /**
     * 发送带优先级的消息
     *
     * @param exchange   交换机
     * @param routingKey 路由键
     * @param message    消息内容
     * @param priority   优先级（0-255）
     */
    public void sendPriorityMessage(String exchange, String routingKey, Object message, int priority) {
        try {
            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            rabbitTemplate.convertAndSend(exchange, routingKey, message, msg -> {
                // 设置消息优先级
                msg.getMessageProperties().setPriority(priority);
                return msg;
            }, correlationData);
            
            log.info("优先级消息发送成功 - exchange: {}, routingKey: {}, priority: {}", 
                    exchange, routingKey, priority);
        } catch (Exception e) {
            log.error("优先级消息发送失败 - exchange: {}, routingKey: {}", exchange, routingKey, e);
            throw new RuntimeException("优先级消息发送失败", e);
        }
    }

    // ==================== 内部消息类定义 ====================

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UserNotificationMessage {
        private String userId;
        private String title;
        private String content;
        private Integer type;
        private Long timestamp;
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class EmailMessage {
        private String to;
        private String subject;
        private String content;
        private Integer type;
        private Long timestamp;
    }


}
