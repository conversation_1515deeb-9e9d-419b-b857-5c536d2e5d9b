package com.subfg.common.service;

import java.io.IOException;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

import com.rabbitmq.client.Channel;
import com.subfg.common.config.RabbitMQConfig;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息消费者服务
 * 处理各种类型的消息消费
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageConsumerService {

    /**
     * 消费用户通知消息
     *
     * @param message 消息内容
     * @param channel 通道
     * @param msg     原始消息
     */
    @RabbitListener(queues = RabbitMQConfig.USER_NOTIFICATION_QUEUE)
    public void handleUserNotification(MessageProducerService.UserNotificationMessage message, 
                                     Channel channel, Message msg) {
        try {
            log.info("收到用户通知消息 - userId: {}, title: {}", message.getUserId(), message.getTitle());
            
            // 处理用户通知逻辑
            processUserNotification(message);
            
            // 手动确认消息
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
            log.info("用户通知消息处理成功 - userId: {}", message.getUserId());
            
        } catch (Exception e) {
            log.error("用户通知消息处理失败 - userId: {}", message.getUserId(), e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(msg.getMessageProperties().getDeliveryTag(), false, true);
            } catch (IOException ioException) {
                log.error("消息拒绝失败", ioException);
            }
        }
    }

    /**
     * 消费邮件发送消息
     *
     * @param message 邮件消息
     * @param channel 通道
     * @param msg     原始消息
     */
    @RabbitListener(queues = RabbitMQConfig.EMAIL_QUEUE)
    public void handleEmailMessage(MessageProducerService.EmailMessage message, 
                                 Channel channel, Message msg) {
        try {
            log.info("收到邮件发送消息 - to: {}, subject: {}", message.getTo(), message.getSubject());
            
            // 处理邮件发送逻辑
            processEmailMessage(message);
            
            // 手动确认消息
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
            log.info("邮件发送消息处理成功 - to: {}", message.getTo());
            
        } catch (Exception e) {
            log.error("邮件发送消息处理失败 - to: {}", message.getTo(), e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(msg.getMessageProperties().getDeliveryTag(), false, true);
            } catch (IOException ioException) {
                log.error("消息拒绝失败", ioException);
            }
        }
    }



    /**
     * 消费死信消息
     *
     * @param message 死信消息
     * @param channel 通道
     * @param msg     原始消息
     */
    @RabbitListener(queues = RabbitMQConfig.DEAD_LETTER_QUEUE)
    public void handleDeadLetterMessage(Object message, Channel channel, Message msg) {
        try {
            log.warn("收到死信消息 - message: {}", message);
            
            // 处理死信消息逻辑（记录、告警等）
            processDeadLetterMessage(message, msg);
            
            // 手动确认消息
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
            log.info("死信消息处理完成");
            
        } catch (Exception e) {
            log.error("死信消息处理失败", e);
            try {
                // 直接确认，避免死信消息再次进入死信队列
                channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException ioException) {
                log.error("死信消息确认失败", ioException);
            }
        }
    }

    // ==================== 私有处理方法 ====================

    /**
     * 处理用户通知消息
     */
    private void processUserNotification(MessageProducerService.UserNotificationMessage message) {
        // TODO: 实现用户通知处理逻辑
        // 例如：保存到数据库、推送到前端、发送短信等
        log.info("处理用户通知 - userId: {}, title: {}, content: {}", 
                message.getUserId(), message.getTitle(), message.getContent());
        
        // 模拟处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 处理邮件发送消息
     */
    private void processEmailMessage(MessageProducerService.EmailMessage message) {
        // TODO: 实现邮件发送逻辑
        // 可以调用现有的EmailService
        log.info("处理邮件发送 - to: {}, subject: {}, type: {}", 
                message.getTo(), message.getSubject(), message.getType());
        
        // 模拟处理时间
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }



    /**
     * 处理死信消息
     */
    private void processDeadLetterMessage(Object message, Message msg) {
        // TODO: 实现死信消息处理逻辑
        // 例如：记录到数据库、发送告警、人工处理等
        log.warn("处理死信消息 - exchange: {}, routingKey: {}, message: {}", 
                msg.getMessageProperties().getReceivedExchange(),
                msg.getMessageProperties().getReceivedRoutingKey(),
                message);
    }
}
