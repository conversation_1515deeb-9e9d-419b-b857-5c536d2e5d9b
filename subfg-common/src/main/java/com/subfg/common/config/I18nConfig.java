package com.subfg.common.config;

import java.util.Arrays;
import java.util.Locale;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

/**
 * 国际化配置类
 * 通过请求头 Accept-Language 实现语言切换
 */
@Configuration
public class I18nConfig {

    /**
     * 配置消息源
     * 用于读取国际化资源文件
     */
    @Bean
    public ResourceBundleMessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        // 设置资源文件基础名称
        messageSource.setBasename("i18n/messages");
        // 设置默认编码
        messageSource.setDefaultEncoding("UTF-8");
        // 设置缓存时间（秒），-1表示永久缓存
        messageSource.setCacheSeconds(3600);
        // 当找不到对应的code时，返回code本身而不是抛出异常
        messageSource.setUseCodeAsDefaultMessage(true);
        return messageSource;
    }

    /**
     * 配置区域解析器
     * 通过请求头 Accept-Language 确定用户的语言环境
     */
    @Bean
    public LocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver localeResolver = new AcceptHeaderLocaleResolver();
        // 设置默认语言为中文
        localeResolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        // 设置支持的语言列表
        localeResolver.setSupportedLocales(Arrays.asList(
            Locale.SIMPLIFIED_CHINESE,  // zh-CN
            Locale.US                   // en-US
        ));
        return localeResolver;
    }
}
