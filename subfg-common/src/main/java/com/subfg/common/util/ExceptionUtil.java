package com.subfg.common.util;

import com.subfg.common.exception.BusinessException;

/**
 * 异常工具类
 * 提供便捷的异常抛出方法
 */
public class ExceptionUtil {

    /**
     * 抛出业务异常
     *
     * @param messageCode 消息代码
     */
    public static void throwBusiness(String messageCode) {
        throw new BusinessException(messageCode);
    }

    /**
     * 抛出业务异常
     *
     * @param messageCode 消息代码
     * @param args        参数
     */
    public static void throwBusiness(String messageCode, Object... args) {
        throw new BusinessException(messageCode, args);
    }

    /**
     * 抛出业务异常
     *
     * @param code        错误码
     * @param messageCode 消息代码
     */
    public static void throwBusiness(Integer code, String messageCode) {
        throw new BusinessException(code, messageCode);
    }

    /**
     * 抛出业务异常
     *
     * @param code        错误码
     * @param messageCode 消息代码
     * @param args        参数
     */
    public static void throwBusiness(Integer code, String messageCode, Object... args) {
        throw new BusinessException(code, messageCode, args);
    }

    /**
     * 条件判断，如果为真则抛出业务异常
     *
     * @param condition   条件
     * @param messageCode 消息代码
     */
    public static void throwIf(boolean condition, String messageCode) {
        if (condition) {
            throwBusiness(messageCode);
        }
    }

    /**
     * 条件判断，如果为真则抛出业务异常
     *
     * @param condition   条件
     * @param messageCode 消息代码
     * @param args        参数
     */
    public static void throwIf(boolean condition, String messageCode, Object... args) {
        if (condition) {
            throwBusiness(messageCode, args);
        }
    }

    /**
     * 条件判断，如果为真则抛出业务异常
     *
     * @param condition   条件
     * @param code        错误码
     * @param messageCode 消息代码
     */
    public static void throwIf(boolean condition, Integer code, String messageCode) {
        if (condition) {
            throwBusiness(code, messageCode);
        }
    }

    /**
     * 对象为空则抛出异常
     *
     * @param obj         对象
     * @param messageCode 消息代码
     */
    public static void throwIfNull(Object obj, String messageCode) {
        throwIf(obj == null, messageCode);
    }

    /**
     * 字符串为空则抛出异常
     *
     * @param str         字符串
     * @param messageCode 消息代码
     */
    public static void throwIfEmpty(String str, String messageCode) {
        throwIf(str == null || str.trim().isEmpty(), messageCode);
    }
}
