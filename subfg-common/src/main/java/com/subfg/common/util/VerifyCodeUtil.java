package com.subfg.common.util;

import java.util.Random;

/**
 * 验证码生成工具类
 * 提供生成数字、字母、混合类型验证码的功能
 */
public class VerifyCodeUtil {

    private static final String NUMBERS = "0123456789";
    private static final String LETTERS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String MIXED = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    /**
     * 生成纯数字验证码
     *
     * @param length 验证码长度
     * @return 指定长度的数字验证码
     */
    public static String generateNumberCode(int length) {
        return generateCode(NUMBERS, length);
    }

    /**
     * 生成纯字母验证码（大小写混合）
     *
     * @param length 验证码长度
     * @return 指定长度的字母验证码
     */
    public static String generateLetterCode(int length) {
        return generateCode(LETTERS, length);
    }

    /**
     * 生成混合验证码（数字+字母）
     *
     * @param length 验证码长度
     * @return 指定长度的混合验证码
     */
    public static String generateMixedCode(int length) {
        return generateCode(MIXED, length);
    }

    /**
     * 根据指定字符集生成验证码
     *
     * @param characters 字符集
     * @param length     验证码长度
     * @return 生成的验证码
     */
    private static String generateCode(String characters, int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("验证码长度必须大于0");
        }

        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(characters.length());
            code.append(characters.charAt(index));
        }

        return code.toString();
    }

    /**
     * 生成指定范围内的随机数验证码
     *
     * @param min 最小值
     * @param max 最大值
     * @return 范围内的随机数
     */
    public static int generateNumberCode(int min, int max) {
        if (min >= max) {
            throw new IllegalArgumentException("最小值必须小于最大值");
        }

        Random random = new Random();
        return random.nextInt(max - min + 1) + min;
    }
}