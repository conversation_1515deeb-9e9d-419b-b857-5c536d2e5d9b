package com.subfg.common.util;

import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * 国际化工具类
 * 提供便捷的国际化消息获取方法
 */
@Component
public class I18nUtil {

    private static MessageSource messageSource;

    public I18nUtil(MessageSource messageSource) {
        I18nUtil.messageSource = messageSource;
    }

    /**
     * 获取国际化消息
     *
     * @param code 消息代码
     * @return 国际化消息
     */
    public static String getMessage(String code) {
        return getMessage(code, (Object[]) null);
    }

    /**
     * 获取国际化消息
     *
     * @param code 消息代码
     * @param args 参数
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args) {
        return getMessage(code, args, code);
    }

    /**
     * 获取国际化消息
     *
     * @param code           消息代码
     * @param args           参数
     * @param defaultMessage 默认消息
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args, String defaultMessage) {
        Locale locale = LocaleContextHolder.getLocale();
        return messageSource.getMessage(code, args, defaultMessage, locale);
    }

    /**
     * 获取指定语言的国际化消息
     *
     * @param code   消息代码
     * @param locale 语言环境
     * @return 国际化消息
     */
    public static String getMessage(String code, Locale locale) {
        return getMessage(code, null, code, locale);
    }

    /**
     * 获取指定语言的国际化消息
     *
     * @param code           消息代码
     * @param args           参数
     * @param defaultMessage 默认消息
     * @param locale         语言环境
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args, String defaultMessage, Locale locale) {
        return messageSource.getMessage(code, args, defaultMessage, locale);
    }

    /**
     * 获取当前语言环境
     *
     * @return 当前语言环境
     */
    public static Locale getCurrentLocale() {
        return LocaleContextHolder.getLocale();
    }

    /**
     * 判断是否为中文环境
     *
     * @return 是否为中文环境
     */
    public static boolean isChinese() {
        Locale locale = getCurrentLocale();
        return Locale.SIMPLIFIED_CHINESE.getLanguage().equals(locale.getLanguage()) ||
               Locale.TRADITIONAL_CHINESE.getLanguage().equals(locale.getLanguage());
    }

    /**
     * 判断是否为英文环境
     *
     * @return 是否为英文环境
     */
    public static boolean isEnglish() {
        Locale locale = getCurrentLocale();
        return Locale.ENGLISH.getLanguage().equals(locale.getLanguage());
    }
}
