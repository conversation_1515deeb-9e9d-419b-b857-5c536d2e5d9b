package com.subfg.common.exception;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 */
public class BusinessException extends RuntimeException {

    private Integer code;
    private String messageCode;
    private Object[] args;

    public BusinessException(String messageCode) {
        super(messageCode);
        this.code = 500;
        this.messageCode = messageCode;
    }

    public BusinessException(Integer code, String messageCode) {
        super(messageCode);
        this.code = code;
        this.messageCode = messageCode;
    }

    public BusinessException(String messageCode, Object... args) {
        super(messageCode);
        this.code = 500;
        this.messageCode = messageCode;
        this.args = args;
    }

    public BusinessException(Integer code, String messageCode, Object... args) {
        super(messageCode);
        this.code = code;
        this.messageCode = messageCode;
        this.args = args;
    }

    public BusinessException(String messageCode, Throwable cause) {
        super(messageCode, cause);
        this.code = 500;
        this.messageCode = messageCode;
    }

    public BusinessException(Integer code, String messageCode, Throwable cause) {
        super(messageCode, cause);
        this.code = code;
        this.messageCode = messageCode;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessageCode() {
        return messageCode;
    }

    public Object[] getArgs() {
        return args;
    }
}
