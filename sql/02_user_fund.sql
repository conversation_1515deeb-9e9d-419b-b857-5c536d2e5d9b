-- 用户资金表建表SQL
-- 对应实体类：com.subfg.domain.entity.user.UserFundPo

CREATE TABLE `user_fund` (
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID（主键）',
    `total_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '总金额',
    `withdraw_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '可提现金额',
    `currency_type` VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
    `total_income` DECIMAL(15,2) DEFAULT 0.00 COMMENT '总收入',
    `total_expense` DECIMAL(15,2) DEFAULT 0.00 COMMENT '总支出',
    `custom_subM_count` INT DEFAULT 0 COMMENT '自定义订阅数量',
    `create_booking_count` INT DEFAULT 0 COMMENT '创建预订数量',
    `create_fg_count` INT DEFAULT 0 COMMENT '创建家庭组数量',
    `join_fg_count` INT DEFAULT 0 COMMENT '加入家庭组数量',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`user_id`),
    KEY `idx_total_amount` (`total_amount`),
    KEY `idx_currency_type` (`currency_type`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_user_fund_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资金表';
