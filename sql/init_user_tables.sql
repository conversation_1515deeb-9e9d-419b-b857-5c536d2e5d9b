-- 用户相关表初始化SQL
-- 包含：user、user_fund、user_auth 三个表的建表语句
-- 执行顺序：user -> user_fund -> user_auth

-- =====================================================
-- 1. 用户表
-- =====================================================
CREATE TABLE `user` (
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID（主键）',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    `phone` BIGINT DEFAULT NULL COMMENT '手机号',
    `wechat_unionid` VARCHAR(100) DEFAULT NULL COMMENT '微信Unionid',
    `password` VARCHAR(255) DEFAULT NULL COMMENT '密码（MD5加密）',
    `salt` VARCHAR(100) DEFAULT NULL COMMENT '密码盐值',
    `role` VARCHAR(50) DEFAULT NULL COMMENT '用户角色',
    `user_name` VARCHAR(100) DEFAULT NULL COMMENT '用户名',
    `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    `motto` VARCHAR(500) DEFAULT NULL COMMENT '个人描述',
    `credit_score` DECIMAL(10,2) DEFAULT NULL COMMENT '信用分数',
    `tags` JSON DEFAULT NULL COMMENT '用户标签',
    `ip_address` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    `create_ip` VARCHAR(50) DEFAULT NULL COMMENT '创建时的IP地址',
    `last_online_time` BIGINT DEFAULT NULL COMMENT '最后在线时间',
    `delete_time` BIGINT DEFAULT NULL COMMENT '删除时间',
    `enable` TINYINT(1) DEFAULT 1 COMMENT '是否启用（1:启用 0:禁用）',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`user_id`),
    UNIQUE KEY `uk_email` (`email`),
    UNIQUE KEY `uk_phone` (`phone`),
    UNIQUE KEY `uk_wechat_unionid` (`wechat_unionid`),
    KEY `idx_user_name` (`user_name`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_enable` (`enable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- =====================================================
-- 2. 用户资金表
-- =====================================================
CREATE TABLE `user_fund` (
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID（主键）',
    `total_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '总金额',
    `withdraw_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '可提现金额',
    `currency_type` VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
    `total_income` DECIMAL(15,2) DEFAULT 0.00 COMMENT '总收入',
    `total_expense` DECIMAL(15,2) DEFAULT 0.00 COMMENT '总支出',
    `custom_subM_count` INT DEFAULT 0 COMMENT '自定义订阅数量',
    `create_booking_count` INT DEFAULT 0 COMMENT '创建预订数量',
    `create_fg_count` INT DEFAULT 0 COMMENT '创建家庭组数量',
    `join_fg_count` INT DEFAULT 0 COMMENT '加入家庭组数量',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`user_id`),
    KEY `idx_total_amount` (`total_amount`),
    KEY `idx_currency_type` (`currency_type`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_user_fund_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资金表';

-- =====================================================
-- 3. 用户第三方认证表
-- =====================================================
CREATE TABLE `user_auth` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `uuid` VARCHAR(100) NOT NULL COMMENT '第三方平台唯一标识',
    `third_account` VARCHAR(100) DEFAULT NULL COMMENT '第三方账号',
    `source` INT NOT NULL COMMENT '第三方平台来源',
    `unbind_time` BIGINT DEFAULT NULL COMMENT '解绑时间',
    `enable` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '第三方头像URL',
    `name` VARCHAR(100) DEFAULT NULL COMMENT '第三方昵称',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_uuid_source` (`uuid`, `source`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_source` (`source`),
    KEY `idx_enable` (`enable`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_user_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户第三方认证表';
