-- 用户第三方认证表建表SQL
-- 对应实体类：com.subfg.domain.entity.user.UserAuthPo

CREATE TABLE `user_auth` (
    `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `uuid` VARCHAR(100) NOT NULL COMMENT '第三方平台唯一标识',
    `third_account` VARCHAR(100) DEFAULT NULL COMMENT '第三方账号',
    `source` VARCHAR(50) NOT NULL COMMENT '第三方平台来源',
    `unbind_time` BIGINT DEFAULT NULL COMMENT '解绑时间',
    `enable` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '第三方头像URL',
    `name` VARCHAR(100) DEFAULT NULL COMMENT '第三方昵称',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_uuid_source` (`uuid`, `source`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_source` (`source`),
    KEY `idx_enable` (`enable`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户第三方认证表';
