package com.subfg.subfgapi.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.subfg.subfgapi.Serivce.ProductService;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/product")
@Tag(name = "产品管理", description = "产品管理相关接口")
public class ProductController {

    private final ProductService productService;



    
}
