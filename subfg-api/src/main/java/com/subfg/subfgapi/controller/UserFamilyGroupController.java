package com.subfg.subfgapi.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.subfg.domain.vo.FamilyGroupVo;
import com.subfg.domain.vo.Result;
import com.subfg.subfgapi.Serivce.UserFamilyGroupService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/user/familyGroup")
@Tag(name = "用户家庭组管理", description = "用户个人家庭组相关接口")
public class UserFamilyGroupController {

    private final UserFamilyGroupService userFamilyGroupService;

    /**
     * 获取当前用户创建的家庭组列表
     * @return 用户创建的家庭组列表
     */
    @Operation(summary = "获取当前用户创建的家庭组列表", description = "获取当前登录用户创建的所有家庭组")
    @GetMapping("/getMyCreatedFamilyGroups")
    public Result<List<FamilyGroupVo>> getMyCreatedFamilyGroups(){
        List<FamilyGroupVo> list = userFamilyGroupService.getMyCreatedFamilyGroups();
        return Result.success(list);
    }

    /**
     * 查询当前用户为团长的家庭组列表
     * @return 当前用户为团长的家庭组列表
     */
    @Operation(summary = "查询当前用户为团长的家庭组列表", description = "查询当前登录用户为团长的所有家庭组")
    @GetMapping("/getMyFamilyGroupsByLeader")
    public Result<List<FamilyGroupVo>> getMyFamilyGroupsByLeader(){
        List<FamilyGroupVo> list = userFamilyGroupService.getMyFamilyGroupsByLeader();
        return Result.success(list);
    }

    /**
     * 获取当前用户加入的家庭组列表
     * @return 用户加入的家庭组列表
     */
    @Operation(summary = "获取当前用户加入的家庭组列表", description = "获取当前登录用户加入的所有家庭组（不包括自己创建的和团长是自己）")
    @GetMapping("/getMyJoinedFamilyGroups")
    public Result<List<FamilyGroupVo>> getMyJoinedFamilyGroups(){
        List<FamilyGroupVo> list = userFamilyGroupService.getMyJoinedFamilyGroups();
        return Result.success(list);
    }

    /**
     * 获取当前用户加入的拼团家庭组列表
     * @return 用户加入的拼团家庭组列表
     */
    @Operation(summary = "获取当前用户加入的拼团家庭组列表", description = "获取当前登录用户加入的所有拼团家庭组")
    @GetMapping("/getMyJoinedGroupBuyingFamilyGroups")
    public Result<List<FamilyGroupVo>> getMyJoinedGroupBuyingFamilyGroups(){
        List<FamilyGroupVo> list = userFamilyGroupService.getMyJoinedGroupBuyingFamilyGroups();
        return Result.success(list);
    }

    /**
     * 加入家庭组
     */
    @Operation(summary = "加入家庭组", description = "加入家庭组")
    @GetMapping("/joinFamilyGroup")
    public Result<String> joinFamilyGroup(@RequestParam String familyGroupId){
        userFamilyGroupService.joinFamilyGroup(familyGroupId);
        return Result.success();
    }

}
