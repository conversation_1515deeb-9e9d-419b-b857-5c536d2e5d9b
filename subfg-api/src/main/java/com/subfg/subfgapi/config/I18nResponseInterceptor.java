package com.subfg.subfgapi.config;

import com.subfg.common.util.I18nUtil;
import com.subfg.domain.vo.Result;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 国际化响应拦截器
 * 用于处理响应中的国际化消息
 */
@RestControllerAdvice
public class I18nResponseInterceptor implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 只处理 Result 类型的响应
        return returnType.getParameterType() == Result.class;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {
        
        if (body instanceof Result<?> result) {
            String message = result.getMessage();
            if (message != null && isI18nCode(message)) {
                // 如果消息是国际化代码，则进行转换
                String i18nMessage = I18nUtil.getMessage(message);
                result.setMessage(i18nMessage);
            }
        }
        
        return body;
    }

    /**
     * 判断是否为国际化代码
     * 简单的判断规则：包含点号的字符串认为是国际化代码
     *
     * @param message 消息
     * @return 是否为国际化代码
     */
    private boolean isI18nCode(String message) {
        return message != null && message.contains(".");
    }
}
