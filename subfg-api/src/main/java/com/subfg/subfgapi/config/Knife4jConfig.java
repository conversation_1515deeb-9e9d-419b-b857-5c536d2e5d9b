package com.subfg.subfgapi.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Knife4j配置类
 * 用于配置API文档的基本信息
 */
@Configuration
public class Knife4jConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("SubFG API 文档")
                        .version("v1.0.0")
                        .description("SubFG 家庭群组管理系统 API 接口文档")
                        .contact(new Contact()
                                .name("SubFG Team")
                                .email("<EMAIL>")
                                .url("https://subfg.com"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")));
    }
}
