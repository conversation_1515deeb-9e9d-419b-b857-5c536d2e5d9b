package com.subfg.subfgapi;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = "com.subfg")
@MapperScan("com.subfg.repository.mapper")
@EnableRabbit
@EnableAsync
@EnableScheduling
public class SubfgApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(SubfgApiApplication.class, args);
    }

}
