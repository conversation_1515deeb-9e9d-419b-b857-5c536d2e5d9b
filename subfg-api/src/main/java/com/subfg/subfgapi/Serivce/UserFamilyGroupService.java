package com.subfg.subfgapi.Serivce;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.subfg.common.constans.FamilyGroupConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.util.IdGeneratorUtil;
import com.subfg.common.util.TimeUtil;
import com.subfg.domain.entity.fg.FgFamilyGroupPo;
import com.subfg.domain.entity.fg.FgMemberPo;
import com.subfg.domain.vo.FamilyGroupVo;
import com.subfg.repository.mapper.FgFamilyGroupMapper;
import com.subfg.repository.mapper.FgMemberMapper;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户家庭组服务类
 * 处理用户个人相关的家庭组操作
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserFamilyGroupService {

    private final FgFamilyGroupMapper familyGroupMapper;
    private final FgMemberMapper fgMemberMapper;

    /**
     * 获取当前用户创建的家庭组列表
     */
    public List<FamilyGroupVo> getMyCreatedFamilyGroups() {
        String currentUserId = StpUtil.getLoginIdAsString();
        log.info("开始查询用户创建的家庭组列表，用户ID：{}", currentUserId);

        // 查询当前用户创建的所有家庭组
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FgFamilyGroupPo::getCreateUserId, currentUserId)
                   .orderByDesc(FgFamilyGroupPo::getCreateTime);

        List<FgFamilyGroupPo> familyGroups = familyGroupMapper.selectList(queryWrapper);

        // 转换为VO对象
        List<FamilyGroupVo> voList = familyGroups.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        log.info("用户创建的家庭组列表查询完成，用户ID：{}，共查询到{}条记录", currentUserId, voList.size());
        return voList;
    }

    /**
     * 获取当前用户加入的家庭组列表（不包括自己创建的和团长是自己的）
     */
    public List<FamilyGroupVo> getMyJoinedFamilyGroups() {
        String currentUserId = StpUtil.getLoginIdAsString();
        log.info("开始查询用户加入的家庭组列表，用户ID：{}", currentUserId);

        // 先查询用户的成员记录
        List<FgMemberPo> memberList = fgMemberMapper.selectByUserId(currentUserId);

        // 获取家庭组ID列表
        List<String> familyGroupIds = memberList.stream()
                .map(FgMemberPo::getFamilyGroupId)
                .collect(Collectors.toList());

        if (familyGroupIds.isEmpty()) {
            log.info("用户未加入任何家庭组，用户ID：{}", currentUserId);
            return List.of();
        }

        // 查询家庭组信息，排除自己创建的和团长是自己的
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FgFamilyGroupPo::getFamilyGroupId, familyGroupIds)
                   .ne(FgFamilyGroupPo::getCreateUserId, currentUserId) // 排除自己创建的
                   .ne(FgFamilyGroupPo::getGroupLeaderId, currentUserId) // 排除团长是自己的
                   .orderByDesc(FgFamilyGroupPo::getCreateTime);

        List<FgFamilyGroupPo> familyGroups = familyGroupMapper.selectList(queryWrapper);

        // 转换为VO对象
        List<FamilyGroupVo> voList = familyGroups.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        log.info("用户加入的家庭组列表查询完成，用户ID：{}，共查询到{}条记录", currentUserId, voList.size());
        return voList;
    }

    /**
     * 获取当前用户加入的拼团家庭组列表
     */
    public List<FamilyGroupVo> getMyJoinedGroupBuyingFamilyGroups() {
        String currentUserId = StpUtil.getLoginIdAsString();
        log.info("开始查询用户加入的拼团家庭组列表，用户ID：{}", currentUserId);

        // 先查询用户的成员记录
        List<FgMemberPo> memberList = fgMemberMapper.selectByUserId(currentUserId);

        // 获取家庭组ID列表
        List<String> familyGroupIds = memberList.stream()
                .map(FgMemberPo::getFamilyGroupId)
                .collect(Collectors.toList());

        if (familyGroupIds.isEmpty()) {
            log.info("用户未加入任何家庭组，用户ID：{}", currentUserId);
            return List.of();
        }

        // 查询拼团家庭组信息，状态为组建中
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FgFamilyGroupPo::getFamilyGroupId, familyGroupIds)
                   .eq(FgFamilyGroupPo::getGroupType, FamilyGroupConstants.GroupType.GROUP_BUYING) // 只查询拼团家庭组
                   .eq(FgFamilyGroupPo::getFamilyGroupStatus, FamilyGroupConstants.Status.BUILDING) // 状态为组建中
                   .orderByDesc(FgFamilyGroupPo::getCreateTime);

        List<FgFamilyGroupPo> familyGroups = familyGroupMapper.selectList(queryWrapper);

        // 转换为VO对象
        List<FamilyGroupVo> voList = familyGroups.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        log.info("用户加入的拼团家庭组列表查询完成，用户ID：{}，共查询到{}条记录", currentUserId, voList.size());
        return voList;
    }

    /**
     * 获取当前用户为团长的家庭组列表
     */
    public List<FamilyGroupVo> getMyFamilyGroupsByLeader() {
        String currentUserId = StpUtil.getLoginIdAsString();
        log.info("开始查询用户为团长的家庭组列表，用户ID：{}", currentUserId);

        // 查询当前用户为团长的所有家庭组
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FgFamilyGroupPo::getGroupLeaderId, currentUserId)
                   .orderByDesc(FgFamilyGroupPo::getCreateTime);

        List<FgFamilyGroupPo> familyGroups = familyGroupMapper.selectList(queryWrapper);

        // 转换为VO对象
        List<FamilyGroupVo> voList = familyGroups.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        log.info("用户为团长的家庭组列表查询完成，用户ID：{}，共查询到{}条记录", currentUserId, voList.size());
        return voList;
    }

    /**
     * 用户加入家庭组
     * 根据家庭组类型分别处理拼团和自建家庭组的不同业务逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public void joinFamilyGroup(String familyGroupId) {
        String currentUserId = StpUtil.getLoginIdAsString();
        log.info("用户开始加入家庭组，用户ID：{}，家庭组ID：{}", currentUserId, familyGroupId);

        // 1. 查询家庭组信息
        FgFamilyGroupPo familyGroup = familyGroupMapper.selectById(familyGroupId);
        if (familyGroup == null) {
            log.warn("家庭组不存在，家庭组ID：{}", familyGroupId);
            throw new BusinessException(FamilyGroupConstants.ErrorCode.FAMILY_GROUP_NOT_FOUND);
        }

        // 2. 验证基本条件
        validateJoinConditions(familyGroup, currentUserId);

        // 3. 根据家庭组类型分别处理
        if (familyGroup.getGroupType() == FamilyGroupConstants.GroupType.GROUP_BUYING) {
            // 拼团家庭组加入逻辑
            joinGroupBuyingFamilyGroup(familyGroup, currentUserId);
        } else if (familyGroup.getGroupType() == FamilyGroupConstants.GroupType.SELF_BUILT) {
            // 自建家庭组加入逻辑
            joinSelfBuiltFamilyGroup(familyGroup, currentUserId);
        } else {
            log.error("未知的家庭组类型，家庭组ID：{}，类型：{}", familyGroupId, familyGroup.getGroupType());
            throw new BusinessException("family.group.invalid.type");
        }

        log.info("用户成功加入家庭组，用户ID：{}，家庭组ID：{}", currentUserId, familyGroupId);
    }

    /**
     * 验证用户加入家庭组的基本条件
     */
    private void validateJoinConditions(FgFamilyGroupPo familyGroup, String currentUserId) {
        // 1. 检查家庭组状态是否允许加入
        if (familyGroup.getFamilyGroupStatus() != FamilyGroupConstants.Status.BUILDING) {
            log.warn("家庭组状态不允许加入，家庭组ID：{}，状态：{}",
                familyGroup.getFamilyGroupId(), familyGroup.getFamilyGroupStatus());
            throw new BusinessException("family.group.status.not.allow.join");
        }

        // 2. 检查是否为团长或创建者（不能加入自己的家庭组）
        if (currentUserId.equals(familyGroup.getGroupLeaderId()) ||
            currentUserId.equals(familyGroup.getCreateUserId())) {
            log.warn("不能加入自己创建或担任团长的家庭组，用户ID：{}，家庭组ID：{}",
                currentUserId, familyGroup.getFamilyGroupId());
            throw new BusinessException("family.group.cannot.join.own");
        }

        // 3. 检查是否已经加入该家庭组
        FgMemberPo existingMember = fgMemberMapper.selectByFamilyGroupIdAndUserId(
            familyGroup.getFamilyGroupId(), currentUserId);
        if (existingMember != null) {
            log.warn("用户已加入该家庭组，用户ID：{}，家庭组ID：{}",
                currentUserId, familyGroup.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.ALREADY_JOINED);
        }

        // 4. 检查家庭组是否已满
        if (familyGroup.getCurrentMemberCount() >= familyGroup.getSumVacancy()) {
            log.warn("家庭组已满，家庭组ID：{}，当前成员数：{}，总空位数：{}",
                familyGroup.getFamilyGroupId(),
                familyGroup.getCurrentMemberCount(),
                familyGroup.getSumVacancy());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.FAMILY_GROUP_FULL);
        }

        // 5. 对于拼团家庭组，检查是否已过截止时间
        if (familyGroup.getGroupType() == FamilyGroupConstants.GroupType.GROUP_BUYING) {
            if (familyGroup.getDeadline() != null &&
                familyGroup.getDeadline() <= TimeUtil.getCurrentTimestamp()) {
                log.warn("拼团家庭组已过截止时间，家庭组ID：{}，截止时间：{}",
                    familyGroup.getFamilyGroupId(),
                    TimeUtil.timestampToDateTime(familyGroup.getDeadline()));
                throw new BusinessException("family.group.deadline.expired");
            }
        }
    }

    /**
     * 拼团家庭组加入逻辑
     */
    private void joinGroupBuyingFamilyGroup(FgFamilyGroupPo familyGroup, String currentUserId) {
        log.info("用户加入拼团家庭组，用户ID：{}，家庭组ID：{}", currentUserId, familyGroup.getFamilyGroupId());

        Long currentTime = TimeUtil.getCurrentTimestamp();

        // 1. 创建成员记录（拼团家庭组成员直接激活）
        String memberId = IdGeneratorUtil.generateFamilyMemberId();
        FgMemberPo member = new FgMemberPo()
            .setMemberId(memberId)
            .setFamilyGroupId(familyGroup.getFamilyGroupId())
            .setUserId(currentUserId)
            .setStatus(FamilyGroupConstants.MemberStatus.ACTIVATED) // 拼团家庭组成员直接激活
            .setInvitationTime(currentTime)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int insertResult = fgMemberMapper.insert(member);
        if (insertResult <= 0) {
            log.error("创建拼团家庭组成员记录失败，用户ID：{}，家庭组ID：{}",
                currentUserId, familyGroup.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.JOIN_FAILED);
        }

        // 2. 更新家庭组成员数量
        int newMemberCount = familyGroup.getCurrentMemberCount() + 1;
        LambdaUpdateWrapper<FgFamilyGroupPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FgFamilyGroupPo::getFamilyGroupId, familyGroup.getFamilyGroupId())
                    .set(FgFamilyGroupPo::getCurrentMemberCount, newMemberCount)
                    .set(FgFamilyGroupPo::getUpdateTime, currentTime);

        int updateResult = familyGroupMapper.update(null, updateWrapper);
        if (updateResult <= 0) {
            log.error("更新拼团家庭组成员数量失败，家庭组ID：{}，新成员数：{}",
                familyGroup.getFamilyGroupId(), newMemberCount);
            throw new BusinessException(FamilyGroupConstants.ErrorCode.JOIN_FAILED);
        }

        log.info("用户成功加入拼团家庭组，用户ID：{}，家庭组ID：{}，当前成员数：{}",
            currentUserId, familyGroup.getFamilyGroupId(), newMemberCount);
    }

    /**
     * 自建家庭组加入逻辑
     */
    private void joinSelfBuiltFamilyGroup(FgFamilyGroupPo familyGroup, String currentUserId) {
        log.info("用户加入自建家庭组，用户ID：{}，家庭组ID：{}", currentUserId, familyGroup.getFamilyGroupId());

        Long currentTime = TimeUtil.getCurrentTimestamp();

        // 1. 创建成员记录（自建家庭组成员待激活）
        String memberId = IdGeneratorUtil.generateFamilyMemberId();
        FgMemberPo member = new FgMemberPo()
            .setMemberId(memberId)
            .setFamilyGroupId(familyGroup.getFamilyGroupId())
            .setUserId(currentUserId)
            .setStatus(FamilyGroupConstants.MemberStatus.PENDING) // 自建家庭组成员待激活
            .setInvitationTime(currentTime)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int insertResult = fgMemberMapper.insert(member);
        if (insertResult <= 0) {
            log.error("创建自建家庭组成员记录失败，用户ID：{}，家庭组ID：{}",
                currentUserId, familyGroup.getFamilyGroupId());
            throw new BusinessException(FamilyGroupConstants.ErrorCode.JOIN_FAILED);
        }

        // 2. 更新家庭组成员数量和最新加入时间
        int newMemberCount = familyGroup.getCurrentMemberCount() + 1;
        LambdaUpdateWrapper<FgFamilyGroupPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FgFamilyGroupPo::getFamilyGroupId, familyGroup.getFamilyGroupId())
                    .set(FgFamilyGroupPo::getCurrentMemberCount, newMemberCount)
                    .set(FgFamilyGroupPo::getLatestJoinTime, currentTime) // 自建家庭组需要更新最新加入时间
                    .set(FgFamilyGroupPo::getUpdateTime, currentTime);

        int updateResult = familyGroupMapper.update(null, updateWrapper);
        if (updateResult <= 0) {
            log.error("更新自建家庭组成员数量失败，家庭组ID：{}，新成员数：{}",
                familyGroup.getFamilyGroupId(), newMemberCount);
            throw new BusinessException(FamilyGroupConstants.ErrorCode.JOIN_FAILED);
        }

        log.info("用户成功加入自建家庭组，用户ID：{}，家庭组ID：{}，当前成员数：{}",
            currentUserId, familyGroup.getFamilyGroupId(), newMemberCount);
    }

    /**
     * 将实体对象转换为VO对象
     */
    private FamilyGroupVo convertToVo(FgFamilyGroupPo po) {
        FamilyGroupVo vo = new FamilyGroupVo();
        vo.setFamilyGroupId(po.getFamilyGroupId());
        vo.setFamilyGroupName(po.getFamilyGroupName());
        vo.setDescription(po.getDescription());
        vo.setGroupType(po.getGroupType());
        vo.setGroupTypeName(getGroupTypeName(po.getGroupType()));
        vo.setFamilyGroupStatus(po.getFamilyGroupStatus());
        vo.setStatusName(getStatusName(po.getFamilyGroupStatus()));
        vo.setProductId(po.getProductId());
        vo.setRegionId(po.getRegionId());
        vo.setPlanId(po.getPlanId());
        vo.setAmount(po.getAmount());
        vo.setBillingCycle(po.getBillingCycle());
        vo.setCurrentMemberCount(po.getCurrentMemberCount());
        vo.setSumVacancy(po.getSumVacancy());
        vo.setDeadline(po.getDeadline());
        vo.setGroupLeaderId(po.getGroupLeaderId());
        vo.setCreateUserId(po.getCreateUserId());
        vo.setCreateTime(po.getCreateTime());
        vo.setUpdateTime(po.getUpdateTime());
        vo.setLatestJoinTime(po.getLatestJoinTime());
        vo.setLaunchTime(po.getLaunchTime());
        vo.setIsConverted(po.getIsConverted());
        return vo;
    }

    /**
     * 获取家庭组类型名称
     */
    private String getGroupTypeName(Integer groupType) {
        if (groupType == null) {
            return null;
        }
        switch (groupType) {
            case FamilyGroupConstants.GroupType.SELF_BUILT:
                return "自建家庭组";
            case FamilyGroupConstants.GroupType.GROUP_BUYING:
                return "拼团家庭组";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取家庭组状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case FamilyGroupConstants.Status.REVIEWING:
                return "审核中";
            case FamilyGroupConstants.Status.BUILDING:
                return "组建中";
            case FamilyGroupConstants.Status.LAUNCHED:
                return "已发车";
            case FamilyGroupConstants.Status.CLOSED:
                return "已关闭";
            case FamilyGroupConstants.Status.REVIEW_REJECTED:
                return "审核未通过";
            default:
                return "未知状态";
        }
    }
}
