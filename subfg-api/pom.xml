<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.subfg</groupId>
        <artifactId>subfg-v3</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>subfg-api</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>subfg-api</name>
    <description>subfg-api</description>
    <properties>
        <java.version>21</java.version>
    </properties>

    <dependencies>
        <!-- 引用公共模块 -->
        <dependency>
            <groupId>com.subfg</groupId>
            <artifactId>subfg-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 引用领域模块 -->
        <dependency>
            <groupId>com.subfg</groupId>
            <artifactId>subfg-domain</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 引用数据访问模块 -->
        <dependency>
            <groupId>com.subfg</groupId>
            <artifactId>subfg-repository</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- MyBatis Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>3.5.12</version>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
