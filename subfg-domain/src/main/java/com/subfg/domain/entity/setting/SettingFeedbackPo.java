package com.subfg.domain.entity.setting;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设置反馈实体类
 * 对应数据库表：setting_feedback
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("setting_feedback")
public class SettingFeedbackPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 目标
     */
    @TableField("target")
    private String target;

    /**
     * 处理方式
     */
    @TableField("dispose")
    private String dispose;

    /**
     * 类型
     */
    @TableField("type")
    private String type;

    /**
     * 描述
     */
    @TableField("describe")
    private String describe;

    /**
     * 图标URL
     */
    @TableField("icon_url")
    private String iconUrl;

    /**
     * 发展
     */
    @TableField("evolve")
    private Integer evolve;

    /**
     * 评估
     */
    @TableField("evaluate")
    private Integer evaluate;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 结束时间
     */
    @TableField("over_time")
    private Long overTime;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Integer enable;

    /**
     * 外部URL
     */
    @TableField("external_url")
    private String externalUrl;
}
