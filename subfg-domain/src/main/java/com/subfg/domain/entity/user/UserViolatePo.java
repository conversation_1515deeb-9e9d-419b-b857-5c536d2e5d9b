package com.subfg.domain.entity.user;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户违规实体类
 * 对应数据库表：user_violate
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_violate")
public class UserViolatePo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 违规ID（主键）
     */
    @TableId("violate_id")
    private String violateId;

    /**
     * 违规用户
     */
    @TableField("violate_user")
    private Integer violateUser;

    /**
     * 违规类型
     */
    @TableField("violate_type")
    private String violateType;

    /**
     * 违规描述
     */
    @TableField("describe")
    private String describe;

    /**
     * 违规图片
     */
    @TableField("violate_img")
    private String violateImg;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;
}
