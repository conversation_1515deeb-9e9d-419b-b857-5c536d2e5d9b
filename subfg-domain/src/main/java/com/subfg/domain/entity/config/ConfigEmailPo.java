package com.subfg.domain.entity.config;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 邮箱配置实体类
 * 对应数据库表：config_email
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("config_email")
public class ConfigEmailPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("id")
    private Integer id;

    /**
     * 账号
     */
    @TableField("account")
    private String account;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * SMTP服务器地址
     */
    @TableField("host")
    private String host;

    /**
     * 邮箱密码
     */
    @TableField("password")
    private String password;

    /**
     * SMTP端口
     */
    @TableField("port")
    private Integer port;

    /**
     * 邮件协议
     */
    @TableField("protocol")
    private String protocol;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Boolean enable;
}
