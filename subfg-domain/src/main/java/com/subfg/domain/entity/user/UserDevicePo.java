package com.subfg.domain.entity.user;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户设备信息实体类
 * 对应数据库表：user_device
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_device")
public class UserDevicePo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 设备型号
     */
    @TableField("device_model")
    private String deviceModel;

    /**
     * 设备图标
     */
    @TableField("device_icon")
    private String deviceIcon;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 最后在线时间
     */
    @TableField("last_one_time")
    private Long lastOneTime;

    /**
     * 应用版本
     */
    @TableField("app_version")
    private String appVersion;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Integer enable;
}
