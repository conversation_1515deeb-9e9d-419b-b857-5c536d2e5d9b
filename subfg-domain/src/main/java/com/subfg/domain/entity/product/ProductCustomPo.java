package com.subfg.domain.entity.product;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 自定义产品实体类
 * 对应数据库表：product_custom
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_custom")
public class ProductCustomPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 用户
     */
    @TableField("user")
    private String user;

    /**
     * 产品名称
     */
    @TableField("name")
    private String name;

    /**
     * 图标URI
     */
    @TableField("icon_uri")
    private String iconUri;

    /**
     * 产品URL
     */
    @TableField("product_url")
    private String productUrl;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 删除标志
     */
    @TableField("del_flag")
    private Integer delFlag;
}
