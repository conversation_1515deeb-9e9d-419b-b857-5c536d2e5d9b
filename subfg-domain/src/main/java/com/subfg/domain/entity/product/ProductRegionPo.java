package com.subfg.domain.entity.product;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 产品地区实体类
 * 对应数据库表：product_region
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_region")
public class ProductRegionPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("id")
    private Integer id;

    /**
     * 货币ID
     */
    @TableField("currency_id")
    private Integer currencyId;

    /**
     * 中文名称
     */
    @TableField("name_cn")
    private String nameCn;

    /**
     * 英文名称
     */
    @TableField("name")
    private String name;

    /**
     * 地区代码
     */
    @TableField("area_code")
    private String areaCode;

    /**
     * 国家代码
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 图标URL
     */
    @TableField("icon_url")
    private String iconUrl;

    /**
     * 创建者
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新者
     */
    @TableField("updator")
    private String updator;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 是否热门
     */
    @TableField("popular")
    private Boolean popular;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Boolean enable;

    /**
     * 货币代码
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Long deleteTime;

    /**
     * 删除者
     */
    @TableField("delete")
    private String delete;

    /**
     * 货币人民币汇率
     */
    @TableField("currency_cny_rate")
    private BigDecimal currencyCnyRate;
}
