package com.subfg.domain.entity.config;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * OSS配置实体类
 * 对应数据库表：config_oss
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("config_oss")
public class ConfigOssPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配置名称
     */
    @TableField("name")
    private String name;

    /**
     * OSS类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 访问ID
     */
    @TableField("access_id")
    private String accessId;

    /**
     * 访问密钥
     */
    @TableField("access_key")
    private String accessKey;

    /**
     * 端点
     */
    @TableField("endpoint")
    private String endpoint;

    /**
     * 存储桶
     */
    @TableField("bucket")
    private String bucket;

    /**
     * 主机地址
     */
    @TableField("host")
    private String host;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Boolean enable;
}
