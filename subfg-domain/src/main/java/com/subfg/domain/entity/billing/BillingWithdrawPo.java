package com.subfg.domain.entity.billing;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 计费提现实体类
 * 对应数据库表：billing_withdraw
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("billing_withdraw")
public class BillingWithdrawPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号（主键）
     */
    @TableId("order_no")
    private String orderNo;

    /**
     * 渠道单号
     */
    @TableField("channel_no")
    private String channelNo;

    /**
     * 渠道
     */
    @TableField("channel")
    private Integer channel;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 订单金额
     */
    @TableField("order_amount")
    private BigDecimal orderAmount;

    /**
     * 实际支付金额
     */
    @TableField("actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 服务费
     */
    @TableField("service_fee")
    private BigDecimal serviceFee;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 完成时间
     */
    @TableField("complete_time")
    private Long completeTime;

    /**
     * 第三方信息
     */
    @TableField("third_info")
    private String thirdInfo;

    /**
     * 原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 旧时间
     */
    @TableField("old_time")
    private Long oldTime;

    /**
     * 审核用户ID
     */
    @TableField("check_user_id")
    private String checkUserId;

    /**
     * 审核次数
     */
    @TableField("check_count")
    private Integer checkCount;

    /**
     * 审核时间
     */
    @TableField("check_time")
    private Long checkTime;
}
