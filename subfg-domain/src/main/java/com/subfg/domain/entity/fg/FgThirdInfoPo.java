package com.subfg.domain.entity.fg;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 家庭组第三方信息实体类
 * 对应数据库表：fg_third_info
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_third_info")
public class FgThirdInfoPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 家庭组ID（主键）
     */
    @TableId("family_group_id")
    private String familyGroupId;

    /**
     * 首次计费日期
     */
    @TableField("first_billing_date")
    private String firstBillingDate;

    /**
     * 计费周期
     */
    @TableField("billing_cycle")
    private Integer billingCycle;

    /**
     * 支付方式
     */
    @TableField("payment_method")
    private String paymentMethod;

    /**
     * 免费试用
     */
    @TableField("free_trial")
    private String freeTrial;

    /**
     * 空位数
     */
    @TableField("vacancy")
    private Integer vacancy;

    /**
     * 计费周期名称
     */
    @TableField("billing_cycle_name")
    private String billingCycleName;
}
