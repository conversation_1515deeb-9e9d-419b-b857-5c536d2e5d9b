package com.subfg.domain.entity.comment;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 评论实体类
 * 对应数据库表：comment
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("comment")
public class CommentPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 家庭组ID
     */
    @TableField("family_group_id")
    private String familyGroupId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 提及用户
     */
    @TableField("mention_users")
    private String mentionUsers;

    /**
     * 评论内容
     */
    @TableField("content")
    private String content;

    /**
     * 点赞数
     */
    @TableField("like_count")
    private Integer likeCount;

    /**
     * 反对数
     */
    @TableField("sick_of_count")
    private Integer sickOfCount;

    /**
     * 回复数
     */
    @TableField("reply_count")
    private Integer replyCount;

    /**
     * 是否热门
     */
    @TableField("is_popular")
    private Boolean isPopular;

    /**
     * 评分
     */
    @TableField("score")
    private Integer score;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 是否编辑
     */
    @TableField("is_edit")
    private String isEdit;

    /**
     * 编辑时间
     */
    @TableField("edit_time")
    private Long editTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Long deleteTime;

    /**
     * 是否置顶
     */
    @TableField("is_up")
    private Integer isUp;

    /**
     * 类型
     */
    @TableField("type")
    private Integer type;
}
