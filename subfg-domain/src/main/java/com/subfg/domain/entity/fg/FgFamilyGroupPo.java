package com.subfg.domain.entity.fg;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 家庭组统一实体类
 * 支持自建家庭组和拼团家庭组两种类型
 * 对应数据库表：fg_family_group
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_family_group")
public class FgFamilyGroupPo implements Serializable {

    private static final long serialVersionUID = 1L;

    // ==================== 基本信息 ====================

    /**
     * 家庭组ID（主键）
     */
    @TableId(value = "family_group_id", type = IdType.INPUT)
    private String familyGroupId;

    /**
     * 家庭组名称
     */
    @TableField("family_group_name")
    private String familyGroupName;

    /**
     * 家庭组描述
     */
    @TableField("description")
    private String description;

    /**
     * 家庭组类型（1-自建家庭组，2-拼团家庭组）
     */
    @TableField("group_type")
    private Integer groupType;

    /**
     * 家庭组状态（0-审核中，1-组建中，2-发车中，3-关闭（团长关闭），-1-审核未通过）
     */
    @TableField("family_group_status")
    private Integer familyGroupStatus;

    /**
     * 邀请码
    */
    @TableField("invite_code")
    private String inviteCode;

    /**
     * 家庭组标签
     */
    @TableField(value = "tags",typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    // ==================== 产品信息 ====================

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Integer productId;

    /**
     * 产品分类ID
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 地区ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 套餐ID
     */
    @TableField("plan_id")
    private Integer planId;

    /**
     * 订阅价格
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 计费周期（月）
     */
    @TableField("billing_cycle")
    private Integer billingCycle;

    // ==================== 成员管理 ====================

    /**
     * 当前成员数
     */
    @TableField("current_member_count")
    private Integer currentMemberCount;

    /**
     * 总空位数
     */
    @TableField("sum_vacancy")
    private Integer sumVacancy;

    /**
     * 截止发车时间（拼团家庭组专用）
     */
    @TableField("deadline")
    private Long deadline;

    // ==================== 用户信息 ====================

    /**
     * 团长用户ID
     */
    @TableField("group_leader_id")
    private String groupLeaderId;

    /**
     * 创建用户ID
     */
    @TableField("create_user_id")
    private String createUserId;

    // ==================== 时间信息 ====================

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 最新加入时间（自建家庭组专用）
     */
    @TableField("latest_join_time")
    private Long latestJoinTime;

    /**
     * 发车时间 - 拼团转为自建时候设置（拼团家庭组专用）
     */
    @TableField("launch_time")
    private Long launchTime;

    /**
     * 订阅开始时间
     */
    @TableField("subscribe_start_time")
    private Long subscribeStartTime;

    /**
     * 订阅结束时间
     */
    @TableField("subscribe_end_time")
    private Long subscribeEndTime;

    // ==================== 审核信息 ====================

    /**
     * 审核时间
     */
    @TableField("review_time")
    private Long reviewTime;

    /**
     * 审核用户
     */
    @TableField("review_user")
    private String reviewUser;

    /**
     * 审核备注
     */
    @TableField("review_remark")
    private String reviewRemark;

    /**
     * 审核图片（自建家庭组专用）
     */
    @TableField("review_picture")
    private String reviewPicture;

    // ==================== 转换信息 ====================

    /**
     * 是否已转换（0-未转换，1-已转换，拼团家庭组专用）
     */
    @TableField("is_converted")
    private Integer isConverted;

}
