package com.subfg.domain.entity.fg;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 家庭组续费投票主表实体类
 * 对应数据库表：fg_renewal_vote
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_renewal_vote")
public class FgRenewalVotePo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投票ID（主键）
     */
    @TableId(value = "vote_id", type = IdType.ASSIGN_ID)
    private String voteId;

    /**
     * 家庭组ID
     */
    @TableField("family_group_id")
    private String familyGroupId;

    /**
     * 投票标题
     */
    @TableField("vote_title")
    private String voteTitle;

    /**
     * 投票描述
     */
    @TableField("vote_description")
    private String voteDescription;

    /**
     * 续费套餐ID
     */
    @TableField("renewal_plan_id")
    private String renewalPlanId;

    /**
     * 续费金额
     */
    @TableField("renewal_amount")
    private BigDecimal renewalAmount;

    /**
     * 续费周期（月）
     */
    @TableField("renewal_cycle")
    private Integer renewalCycle;

    /**
     * 投票状态（1-进行中，2-通过，3-未通过，4-已取消，5-已过期）
     */
    @TableField("vote_status")
    private Integer voteStatus;

    /**
     * 投票类型（1-续费投票，2-套餐变更投票，3-其他）
     */
    @TableField("vote_type")
    private Integer voteType;

    /**
     * 需要的最少同意票数
     */
    @TableField("required_agree_count")
    private Integer requiredAgreeCount;

    /**
     * 当前同意票数
     */
    @TableField("current_agree_count")
    private Integer currentAgreeCount;

    /**
     * 当前反对票数
     */
    @TableField("current_disagree_count")
    private Integer currentDisagreeCount;

    /**
     * 总投票人数
     */
    @TableField("total_voter_count")
    private Integer totalVoterCount;

    /**
     * 已投票人数
     */
    @TableField("voted_count")
    private Integer votedCount;

    /**
     * 投票开始时间
     */
    @TableField("vote_start_time")
    private Long voteStartTime;

    /**
     * 投票结束时间
     */
    @TableField("vote_end_time")
    private Long voteEndTime;

    /**
     * 投票结果确定时间
     */
    @TableField("vote_result_time")
    private Long voteResultTime;

    /**
     * 发起人用户ID
     */
    @TableField("initiator_user_id")
    private String initiatorUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
