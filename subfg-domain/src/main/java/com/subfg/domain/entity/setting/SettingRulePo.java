package com.subfg.domain.entity.setting;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设置规则实体类
 * 对应数据库表：setting_rule
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("setting_rule")
public class SettingRulePo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 规则名称
     */
    @TableField("name")
    private String name;

    /**
     * 规则值
     */
    @TableField("value")
    private String value;

    /**
     * 描述
     */
    @TableField("describe")
    private String describe;

    /**
     * 图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 更新者
     */
    @TableField("updator")
    private String updator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 附加类
     */
    @TableField("attach_class")
    private String attachClass;
}
