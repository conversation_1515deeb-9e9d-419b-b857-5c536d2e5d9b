package com.subfg.domain.entity.billing;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 计费流水实体类
 * 对应数据库表：billing_flow
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("billing_flow")
public class BillingFlowPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 计费单号（主键）
     */
    @TableId("billing_no")
    private String billingNo;

    /**
     * 渠道单号
     */
    @TableField("channel_no")
    private String channelNo;

    /**
     * 渠道
     */
    @TableField("channel")
    private String channel;

    /**
     * 计费来源
     */
    @TableField("billing_from")
    private String billingFrom;

    /**
     * 计费目标
     */
    @TableField("billing_to")
    private String billingTo;

    /**
     * 实际支付金额
     */
    @TableField("actualpay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 订单金额
     */
    @TableField("order_amount")
    private BigDecimal orderAmount;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private String productId;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 图标URL
     */
    @TableField("icon_url")
    private String iconUrl;

    /**
     * 计费类型
     */
    @TableField("billing_type")
    private Long billingType;

    /**
     * 计费方向
     */
    @TableField("billing_direction")
    private Boolean billingDirection;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 家庭组ID
     */
    @TableField("family_group_id")
    private String familyGroupId;

    /**
     * 成员ID
     */
    @TableField("member_id")
    private String memberId;
}
