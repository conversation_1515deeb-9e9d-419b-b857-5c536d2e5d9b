package com.subfg.domain.entity.user;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户第三方认证实体类
 * 对应数据库表：user_auth
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_auth")
public class UserAuthPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 第三方平台唯一标识
     */
    @TableField("uuid")
    private String uuid;

    /**
     * 第三方账号
     */
    @TableField("third_account")
    private String thirdAccount;

    /**
     * 第三方平台来源
     */
    @TableField("source")
    private String source;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 解绑时间
     */
    @TableField("unbind_time")
    private Long unbindTime;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Boolean enable;

    /**
     * 第三方头像URL
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 第三方昵称
     */
    @TableField("name")
    private String name;
}
