package com.subfg.domain.entity.user;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户收藏实体类
 * 对应数据库表：user_favorite
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_favorite")
public class UserFavoritePo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 家庭组ID
     */
    @TableField("family_group_id")
    private String familyGroupId;

    /**
     * 收藏金额
     */
    @TableField("favorite_amount")
    private BigDecimal favoriteAmount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 收藏类型
     */
    @TableField("type")
    private Integer type;
}
