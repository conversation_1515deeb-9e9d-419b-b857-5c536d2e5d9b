package com.subfg.domain.entity.product;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 产品描述实体类
 * 对应数据库表：product_descibe
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_descibe")
public class ProductDescibePo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 产品名称
     */
    @TableField("name")
    private String name;

    /**
     * 创建者
     */
    @TableField("creator")
    private String creator;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新者
     */
    @TableField("updator")
    private String updator;

    /**
     * 中文描述
     */
    @TableField("describe_cn")
    private String describeCn;

    /**
     * 英文描述
     */
    @TableField("describe_en")
    private String describeEn;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Integer enable;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Long deleteTime;

    /**
     * 删除者
     */
    @TableField("delete")
    private String delete;
}
