package com.subfg.domain.entity.user;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户反馈实体类
 * 对应数据库表：user_feedback
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_feedback")
public class UserFeedbackPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 上一个反馈ID
     */
    @TableField("last_feedback_id")
    private Integer lastFeedbackId;

    /**
     * 用户
     */
    @TableField("user")
    private String user;

    /**
     * 反馈内容
     */
    @TableField("content")
    private String content;

    /**
     * 反馈图片
     */
    @TableField("images")
    private String images;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 评分
     */
    @TableField("rate")
    private Integer rate;

    /**
     * 意见
     */
    @TableField("opinion")
    private String opinion;

    /**
     * 意见图片
     */
    @TableField("opinion_images")
    private String opinionImages;

    /**
     * 服务意见
     */
    @TableField("service_opinion")
    private String serviceOpinion;

    /**
     * 服务图片
     */
    @TableField("service_images")
    private String serviceImages;

    /**
     * 服务ID
     */
    @TableField("service_id")
    private String serviceId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 服务时间
     */
    @TableField("service_time")
    private Long serviceTime;

    /**
     * 完成时间
     */
    @TableField("completion_time")
    private Long completionTime;
}
