package com.subfg.domain.entity.user;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户消息实体类
 * 对应数据库表：user_message
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_message")
public class UserMessagePo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 消息类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 消息枚举
     */
    @TableField("message_enum")
    private Integer messageEnum;

    /**
     * 其他ID
     */
    @TableField("other_id")
    private String otherId;

    /**
     * 消息内容主题
     */
    @TableField("content_subject")
    private String contentSubject;

    /**
     * 是否已读
     */
    @TableField("readed")
    private Boolean readed;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 原始ID
     */
    @TableField("original_id")
    private String originalId;

    /**
     * 成员ID
     */
    @TableField("member_id")
    private String memberId;
}
