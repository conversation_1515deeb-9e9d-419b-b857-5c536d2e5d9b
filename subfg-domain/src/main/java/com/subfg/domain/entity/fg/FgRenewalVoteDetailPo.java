package com.subfg.domain.entity.fg;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 家庭组续费投票详情表实体类
 * 对应数据库表：fg_renewal_vote_detail
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_renewal_vote_detail")
public class FgRenewalVoteDetailPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 详情ID（主键）
     */
    @TableId(value = "detail_id", type = IdType.ASSIGN_ID)
    private String detailId;

    /**
     * 投票ID
     */
    @TableField("vote_id")
    private String voteId;

    /**
     * 家庭组ID
     */
    @TableField("family_group_id")
    private String familyGroupId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 用户昵称（冗余字段，便于查询显示）
     */
    @TableField("user_nickname")
    private String userNickname;

    /**
     * 投票选择（1-同意，2-反对，0-未投票）
     */
    @TableField("vote_choice")
    private Integer voteChoice;

    /**
     * 投票状态（1-已投票，0-未投票）
     */
    @TableField("vote_status")
    private Integer voteStatus;

    /**
     * 投票时间
     */
    @TableField("vote_time")
    private Long voteTime;

    /**
     * 投票理由/备注
     */
    @TableField("vote_reason")
    private String voteReason;

    /**
     * 是否为家庭组管理员
     */
    @TableField("is_admin")
    private Boolean isAdmin;

    /**
     * 用户角色（1-组长，2-成员）
     */
    @TableField("user_role")
    private Integer userRole;

    /**
     * 创建时间（记录生成时间）
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * IP地址（记录投票时的IP）
     */
    @TableField("vote_ip")
    private String voteIp;

    /**
     * 设备信息
     */
    @TableField("device_info")
    private String deviceInfo;
}
