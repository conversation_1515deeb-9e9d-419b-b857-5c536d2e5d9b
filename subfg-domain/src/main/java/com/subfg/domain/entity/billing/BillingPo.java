package com.subfg.domain.entity.billing;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 计费实体类
 * 对应数据库表：billing
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("billing")
public class BillingPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 计费单号（主键）
     */
    @TableId("billing_no")
    private String billingNo;

    /**
     * 渠道单号
     */
    @TableField("channel_no")
    private String channelNo;

    /**
     * 渠道
     */
    @TableField("channel")
    private String channel;

    /**
     * 计费来源
     */
    @TableField("billing_from")
    private String billingFrom;

    /**
     * 计费目标
     */
    @TableField("billing_to")
    private String billingTo;

    /**
     * 实际支付金额
     */
    @TableField("actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 计费类型
     */
    @TableField("billing_type")
    private Integer billingType;

    /**
     * 计费方向
     */
    @TableField("billing_direction")
    private Boolean billingDirection;

    /**
     * 旧时间
     */
    @TableField("old_time")
    private Long oldTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 其他ID
     */
    @TableField("other_id")
    private String otherId;
}
