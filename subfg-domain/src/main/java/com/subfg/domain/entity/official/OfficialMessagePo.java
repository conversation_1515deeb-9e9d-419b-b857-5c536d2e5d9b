package com.subfg.domain.entity.official;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 官方消息实体类
 * 对应数据库表：official_message
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("official_message")
public class OfficialMessagePo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 消息名称
     */
    @TableField("name")
    private String name;

    /**
     * 中文描述
     */
    @TableField("describe_cn")
    private String describeCn;

    /**
     * 英文描述
     */
    @TableField("describe_en")
    private String describeEn;

    /**
     * 消息类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 状态码
     */
    @TableField("status_code")
    private Integer statusCode;

    /**
     * 创建者
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新者
     */
    @TableField("updator")
    private String updator;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Integer enable;
}
