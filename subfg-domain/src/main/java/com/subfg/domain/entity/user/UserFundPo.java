package com.subfg.domain.entity.user;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户资金实体类
 * 对应数据库表：user_fund
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_fund")
public class UserFundPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID（主键）
     */
    @TableId("user_id")
    private String userId;

    /**
     * 总金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 可提现金额
     */
    @TableField("withdraw_amount")
    private BigDecimal withdrawAmount;

    /**
     * 货币类型
     */
    @TableField("currency_type")
    private String currencyType;

    /**
     * 总收入
     */
    @TableField("total_income")
    private BigDecimal totalIncome;

    /**
     * 总支出
     */
    @TableField("total_expense")
    private BigDecimal totalExpense;

    /**
     * 自定义订阅数量
     */
    @TableField("custom_subM_count")
    private Integer customSubMCount;

    /**
     * 创建预订数量
     */
    @TableField("create_booking_count")
    private Integer createBookingCount;

    /**
     * 创建家庭组数量
     */
    @TableField("create_fg_count")
    private Integer createFgCount;

    /**
     * 加入家庭组数量
     */
    @TableField("join_fg_count")
    private Integer joinFgCount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;
}
