package com.subfg.domain.entity.comment;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 评论回复实体类
 * 对应数据库表：comment_reply
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("comment_reply")
public class CommentReplyPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 家庭组ID
     */
    @TableField("family_group_id")
    private String familyGroupId;

    /**
     * 评论ID
     */
    @TableField("comment_id")
    private String commentId;

    /**
     * 回复目标用户
     */
    @TableField("reply_to_user")
    private String replyToUser;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 回复内容
     */
    @TableField("content")
    private String content;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 点赞数
     */
    @TableField("like_count")
    private Integer likeCount;

    /**
     * 反对数
     */
    @TableField("sick_of_count")
    private Integer sickOfCount;

    /**
     * 是否删除
     */
    @TableField("is_delete")
    private Boolean isDelete;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Long deleteTime;

    /**
     * 编辑时间
     */
    @TableField("edit_time")
    private Long editTime;

    /**
     * 回复ID
     */
    @TableField("reply_id")
    private String replyId;

    /**
     * 包含回复
     */
    @TableField("include_reply")
    private Integer includeReply;

    /**
     * 提及用户
     */
    @TableField("mention_users")
    private String mentionUsers;

    /**
     * 是否置顶
     */
    @TableField("is_up")
    private Integer isUp;

    /**
     * 类型
     */
    @TableField("type")
    private Integer type;
}
