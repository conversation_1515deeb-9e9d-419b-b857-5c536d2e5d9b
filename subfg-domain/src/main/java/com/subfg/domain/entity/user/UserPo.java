package com.subfg.domain.entity.user;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户实体类
 * 对应数据库表：user
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user")
public class UserPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID（主键）
     */
    @TableId(value = "user_id", type = IdType.INPUT)
    private String userId;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @TableField("phone")
    private Long phone;

    /**
     * 微信Unionid
     */
    @TableField("wechat_unionid")
    private String wechatUnionid;

    /**
     * 密码（MD5加密）
     */
    @TableField("password")
    private String password;

    /**
     * 密码盐值
     */
    @TableField("salt")
    private String salt;

    /**
     * 用户角色
     */
    @TableField("role")
    private String role;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 头像URL
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 个人描述
     */
    @TableField("motto")
    private String motto;

    /**
     * 信用分数
     */
    @TableField("credit_score")
    private BigDecimal creditScore;

    /**
     * 用户标签
     */
    @TableField(value = "tags",typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 创建时的IP地址
     */
    @TableField("create_ip")
    private String createIp;

    /**
     * 最后在线时间
     */
    @TableField("last_online_time")
    private Long lastOnlineTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Long deleteTime;

    /**
     * 是否启用（1:启用 0:禁用）
     */
    @TableField("enable")
    private Boolean enable;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;
    
}
