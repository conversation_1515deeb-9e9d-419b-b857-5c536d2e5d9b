package com.subfg.domain.entity.billing;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 计费订单实体类
 * 对应数据库表：billing_order
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("billing_order")
public class BillingOrderPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号（主键）
     */
    @TableId("order_no")
    private String orderNo;

    /**
     * 合并单号
     */
    @TableField("merge_no")
    private String mergeNo;

    /**
     * 渠道单号
     */
    @TableField("channel_no")
    private String channelNo;

    /**
     * 退款单号
     */
    @TableField("refund_no")
    private String refundNo;

    /**
     * 优惠券ID
     */
    @TableField("coupon_id")
    private String couponId;

    /**
     * 成员ID
     */
    @TableField("member_id")
    private String memberId;

    /**
     * 家庭组ID
     */
    @TableField("family_group_id")
    private String familyGroupId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 货币
     */
    @TableField("currency")
    private String currency;

    /**
     * 家庭组所有者
     */
    @TableField("family_group_owner")
    private String familyGroupOwner;

    /**
     * 第三方账号
     */
    @TableField("third_account")
    private String thirdAccount;

    /**
     * 计费周期
     */
    @TableField("billing_cycle")
    private Integer billingCycle;

    /**
     * 订单类型
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 折扣
     */
    @TableField("discount")
    private BigDecimal discount;

    /**
     * 订单金额
     */
    @TableField("order_amount")
    private BigDecimal orderAmount;

    /**
     * 支付平台
     */
    @TableField("pay_platform")
    private String payPlatform;

    /**
     * 实际支付金额
     */
    @TableField("actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 支付费率
     */
    @TableField("payment_rate")
    private BigDecimal paymentRate;

    /**
     * 预计退款金额
     */
    @TableField("predict_refund_amount")
    private BigDecimal predictRefundAmount;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 渠道
     */
    @TableField("channel")
    private Integer channel;

    /**
     * 支付状态
     */
    @TableField("payment_status")
    private Integer paymentStatus;

    /**
     * 订单状态
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 预订状态
     */
    @TableField("booking_status")
    private Integer bookingStatus;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 支付时间
     */
    @TableField("payment_time")
    private Long paymentTime;

    /**
     * 服务开始时间
     */
    @TableField("service_start_time")
    private Long serviceStartTime;

    /**
     * 退款申请时间
     */
    @TableField("refund_apply_time")
    private Long refundApplyTime;

    /**
     * 服务结束时间
     */
    @TableField("service_over_time")
    private Long serviceOverTime;

    /**
     * 服务过期时间
     */
    @TableField("service_expire_time")
    private Long serviceExpireTime;

    /**
     * 支付过期时间
     */
    @TableField("payment_expire_time")
    private Long paymentExpireTime;

    /**
     * 退款描述
     */
    @TableField("refund_describe")
    private String refundDescribe;

    /**
     * 支付环境
     */
    @TableField("payment_env")
    private String paymentEnv;

    /**
     * 旧时间
     */
    @TableField("old_time")
    private Long oldTime;
}
