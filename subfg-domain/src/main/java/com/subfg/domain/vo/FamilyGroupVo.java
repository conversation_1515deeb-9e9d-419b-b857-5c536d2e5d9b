package com.subfg.domain.vo;

import java.math.BigDecimal;

import lombok.Data;

/**
 * 家庭组列表返回对象
 */
@Data
public class FamilyGroupVo {

    /**
     * 家庭组ID
     */
    private String familyGroupId;

    /**
     * 家庭组名称
     */
    private String familyGroupName;

    /**
     * 家庭组描述
     */
    private String description;

    /**
     * 家庭组类型（1-自建家庭组，2-拼团家庭组）
     */
    private Integer groupType;

    /**
     * 家庭组类型名称
     */
    private String groupTypeName;

    /**
     * 家庭组状态（0-审核中，1-组建中，2-发车中，3-关闭，-1-审核未通过）
     */
    private Integer familyGroupStatus;

    /**
     * 家庭组状态名称
     */
    private String statusName;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * 地区ID
     */
    private Integer regionId;

    /**
     * 套餐ID
     */
    private Integer planId;

    /**
     * 订阅价格
     */
    private BigDecimal amount;

    /**
     * 计费周期（月）
     */
    private Integer billingCycle;

    /**
     * 当前成员数
     */
    private Integer currentMemberCount;

    /**
     * 总空位数
     */
    private Integer sumVacancy;

    /**
     * 截止发车时间（拼团家庭组专用）
     */
    private Long deadline;

    /**
     * 团长用户ID
     */
    private String groupLeaderId;

    /**
     * 创建用户ID
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 最新加入时间（自建家庭组专用）
     */
    private Long latestJoinTime;

    /**
     * 发车时间（拼团家庭组专用）
     */
    private Long launchTime;

    /**
     * 是否已转换（0-未转换，1-已转换，拼团家庭组专用）
     */
    private Integer isConverted;

}
