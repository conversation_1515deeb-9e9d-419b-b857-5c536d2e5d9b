package com.subfg.domain.vo;

import lombok.Data;

/**
 * 统一响应结果类
 */
@Data
public class Result<T> {

    private Integer code;
    private String message;
    private T data;
    private Long timestamp;

    public Result() {
        this.timestamp = System.currentTimeMillis();
    }

    public static <T> Result<T> success() {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage("common.success");
        return result;
    }

    public static <T> Result<T> success(T data) {
        Result<T> result = success();
        result.setData(data);
        return result;
    }

    public static <T> Result<T> success(String message, T data) {
        Result<T> result = success(data);
        result.setMessage(message);
        return result;
    }

    public static <T> Result<T> error(String message) {
        Result<T> result = new Result<>();
        result.setCode(500);
        result.setMessage(message);
        return result;
    }

    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    /**
     * 使用国际化消息的成功响应
     *
     * @param messageCode 消息代码
     * @param <T>         数据类型
     * @return 响应结果
     */
    public static <T> Result<T> successI18n(String messageCode) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage(messageCode); // 这里存储消息代码，在拦截器中转换
        return result;
    }

    /**
     * 使用国际化消息的成功响应（带数据）
     *
     * @param messageCode 消息代码
     * @param data        数据
     * @param <T>         数据类型
     * @return 响应结果
     */
    public static <T> Result<T> successI18n(String messageCode, T data) {
        Result<T> result = successI18n(messageCode);
        result.setData(data);
        return result;
    }

    /**
     * 使用国际化消息的错误响应
     *
     * @param messageCode 消息代码
     * @param <T>         数据类型
     * @return 响应结果
     */
    public static <T> Result<T> errorI18n(String messageCode) {
        Result<T> result = new Result<>();
        result.setCode(500);
        result.setMessage(messageCode); // 这里存储消息代码，在拦截器中转换
        return result;
    }
}
