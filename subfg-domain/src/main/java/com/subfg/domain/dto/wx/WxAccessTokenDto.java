package com.subfg.domain.dto.wx;

import lombok.Data;

@Data
public class WxAccessTokenDto {
    /** 接口调用凭证 */
    private String access_token;
    /** 凭证有效时间，单位：秒。一般情况下，wx.login 调用成功后，可认为下发登录态在 2 个小时期间内都有效。 */
    private Long expires_in;
    /** 用户刷新access_token */
    private String refresh_token;
    /** 用户唯一标识 */
    private String openid;
    /** 用户授权作用域 */
    private String scope;
    /** 用户统一标识。针对一个微信开放平台帐号下的应用，同一用户的unionid是唯一的。 */
    private String unionid;
}
