package com.subfg.domain.enums;

import lombok.Getter;

/**
 * 三方平台类型枚举
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
public enum ThirdPlatformType {

    /**
     * 微信
     */
    WECHAT("wechat", "微信", "微信开放平台"),

    /**
     * QQ
     */
    QQ("qq", "QQ", "QQ互联平台"),

    /**
     * 谷歌
     */
    GOOGLE("google", "谷歌", "Google OAuth"),

    /**
     * GitHub
     */
    GITHUB("github", "GitHub", "GitHub OAuth"),

    /**
     * 微博
     */
    WEIBO("weibo", "微博", "微博开放平台"),

    /**
     * 支付宝
     */
    ALIPAY("alipay", "支付宝", "支付宝开放平台"),

    /**
     * 钉钉
     */
    DINGTALK("dingtalk", "钉钉", "钉钉开放平台"),

    /**
     * 企业微信
     */
    WECHAT_WORK("wechat_work", "企业微信", "企业微信开放平台"),

    /**
     * 百度
     */
    BAIDU("baidu", "百度", "百度开放平台"),

    /**
     * 抖音
     */
    DOUYIN("douyin", "抖音", "抖音开放平台"),

    /**
     * 小红书
     */
    XIAOHONGSHU("xiaohongshu", "小红书", "小红书开放平台"),

    /**
     * Facebook
     */
    FACEBOOK("facebook", "Facebook", "Facebook Login"),

    /**
     * Twitter/X
     */
    TWITTER("twitter", "Twitter", "Twitter OAuth"),

    /**
     * LinkedIn
     */
    LINKEDIN("linkedin", "LinkedIn", "LinkedIn OAuth"),

    /**
     * Apple
     */
    APPLE("apple", "Apple", "Sign in with Apple");

    /**
     * 平台代码（数据库存储值）
     */
    private final String code;

    /**
     * 平台名称（显示名称）
     */
    private final String name;

    /**
     * 平台描述
     */
    private final String description;

    ThirdPlatformType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 平台代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static ThirdPlatformType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (ThirdPlatformType type : values()) {
            if (type.getCode().equals(code.toLowerCase())) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 平台代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    /**
     * 获取所有平台代码
     *
     * @return 平台代码数组
     */
    public static String[] getAllCodes() {
        ThirdPlatformType[] types = values();
        String[] codes = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            codes[i] = types[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有平台名称
     *
     * @return 平台名称数组
     */
    public static String[] getAllNames() {
        ThirdPlatformType[] types = values();
        String[] names = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            names[i] = types[i].getName();
        }
        return names;
    }
}
