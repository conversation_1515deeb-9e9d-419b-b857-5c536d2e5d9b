package com.subfg.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
@Schema(description = "绑定三方账号请求")
public class BindThirdPartyAccountReq {

    @Schema(description = "三方账号类型", example = "wechat")
    @NotBlank(message = "三方账号类型不能为空")
    @Pattern(regexp = "^(wechat)$", message = "目前只支持微信绑定")
    private String thirdPartyType;

    @Schema(description = "三方平台授权码", example = "wx_auth_code_123456")
    @NotBlank(message = "授权码不能为空")
    private String code;

}
