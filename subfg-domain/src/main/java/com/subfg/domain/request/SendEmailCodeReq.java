package com.subfg.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "发送邮箱验证码请求")
public class SendEmailCodeReq {

    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱地址")
    private String email;

    @Schema(description = "验证码类型:1-注册,2-登录,3-忘记密码,4-换绑邮箱")
    @NotNull(message = "验证码类型不能为空")
    private Integer type;
    
}