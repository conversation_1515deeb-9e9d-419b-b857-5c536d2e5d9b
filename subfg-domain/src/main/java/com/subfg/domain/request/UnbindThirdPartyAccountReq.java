package com.subfg.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
@Schema(description = "解绑三方账号请求")
public class UnbindThirdPartyAccountReq {

    @Schema(description = "三方账号类型", example = "wechat")
    @NotBlank(message = "三方账号类型不能为空")
    @Pattern(regexp = "^(wechat)$", message = "目前只支持微信解绑")
    private String thirdPartyType;

}
