package com.subfg.domain.request;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;



@Getter
@Schema(description = "创建自建家庭组请求")
public class createSelfBuiltFamilyGroupReq {

    /**
     * 家庭组名称
     */
    @Schema(description = "家庭组名称", example = "我的家庭组")
    @NotBlank(message = "家庭组名称不能为空")
    @Size(max = 50, message = "家庭组名称长度不能超过50个字符")
    private String familyGroupName;

    /**
     * 家庭组描述
     */
    @Schema(description = "家庭组描述", example = "这是一个测试家庭组")
    @Size(max = 200, message = "家庭组描述长度不能超过200个字符")
    private String description;

    /**
     * 产品ID
     */
    @Schema(description = "产品ID", example = "1")
    @NotNull(message = "产品ID不能为空")
    @Min(value = 1, message = "产品ID必须大于0")
    private Integer productId;

    /**
     * 地区ID
     */
    @Schema(description = "地区ID", example = "1")
    @NotNull(message = "地区ID不能为空")
    @Min(value = 1, message = "地区ID必须大于0")
    private Integer regionId;

    /**
     * 套餐ID
     */
    @Schema(description = "套餐ID", example = "1")
    @NotNull(message = "套餐ID不能为空")
    @Min(value = 1, message = "套餐ID必须大于0")
    private Integer planId;

    /**
     * 家庭组总空位数
     */
    @Schema(description = "家庭组总空位数", example = "4")
    @NotNull(message = "家庭组总空位数不能为空")
    @Min(value = 2, message = "家庭组成员数量不能少于2人")
    @Max(value = 6, message = "家庭组成员数量不能超过6人")
    private Integer familyGroupTotalVacancy;

    /**
     * 计费周期
     */
    @Schema(description = "计费周期（月）", example = "1")
    @Min(value = 1, message = "计费周期必须大于0")
    private Integer billingCycle;

    /**
     * 价格
     */
    @Schema(description = "价格", example = "99.99")
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.01", message = "价格必须大于0")
    private BigDecimal amount;

}