<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.subfg</groupId>
        <artifactId>subfg-v3</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>subfg-domain</artifactId>
    <name>subfg-domain</name>
    <description>领域模型模块，包含 Entity、DTO、VO 等数据模型类</description>

    <properties>
        <java.version>21</java.version>
    </properties>

    <dependencies>
        <!-- 引用公共模块 -->
        <dependency>
            <groupId>com.subfg</groupId>
            <artifactId>subfg-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- MyBatis Plus 注解支持 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>3.5.12</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.5.12</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

</project>
